import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { roomService } from "../../lib/roomService";
import type { RoomWithDetails } from "../../lib/roomService";
import { useAuth } from "../../contexts/AuthContext";

const bulkShareSchema = z.object({
  roomId: z.string().min(1, "Please select a room"),
  permission: z.enum(["view", "download"]),
});

type BulkShareFormData = z.infer<typeof bulkShareSchema>;

interface BulkShareToRoomProps {
  documents: DocumentFile[];
  onClose: () => void;
  onShareComplete?: () => void;
}

export function BulkShareToRoom({
  documents,
  onClose,
  onShareComplete,
}: BulkShareToRoomProps) {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<RoomWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRooms, setIsLoadingRooms] = useState(true);
  const [shareProgress, setShareProgress] = useState<{
    current: number;
    total: number;
    currentDocument: string;
  } | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BulkShareFormData>({
    defaultValues: {
      permission: "download",
    },
  });

  useEffect(() => {
    loadUserRooms();
  }, []);

  const loadUserRooms = async () => {
    if (!user) return;

    try {
      setIsLoadingRooms(true);
      const userRooms = await roomService.getUserRooms(user.id);
      setRooms(userRooms);
    } catch (error: any) {
      console.error("Load rooms error:", error);
      toast.error("Failed to load rooms");
    } finally {
      setIsLoadingRooms(false);
    }
  };

  const onSubmit = async (data: BulkShareFormData) => {
    if (!user) {
      toast.error("You must be logged in to share documents");
      return;
    }

    try {
      setIsLoading(true);
      setShareProgress({
        current: 0,
        total: documents.length,
        currentDocument: "",
      });

      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (let i = 0; i < documents.length; i++) {
        const document = documents[i];
        
        setShareProgress({
          current: i + 1,
          total: documents.length,
          currentDocument: document.title,
        });

        try {
          await roomService.shareDocumentToRoom(
            document.id,
            data.roomId,
            user.id,
            data.permission
          );
          successCount++;
        } catch (error: any) {
          errorCount++;
          errors.push(`${document.title}: ${error.message}`);
        }

        // Small delay to prevent overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      setShareProgress(null);

      if (successCount > 0) {
        toast.success(
          `Successfully shared ${successCount} document${successCount > 1 ? 's' : ''} to room`
        );
      }

      if (errorCount > 0) {
        console.error("Bulk share errors:", errors);
        toast.error(
          `Failed to share ${errorCount} document${errorCount > 1 ? 's' : ''}. Check console for details.`
        );
      }

      if (successCount > 0) {
        onShareComplete?.();
        onClose();
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to share documents");
      setShareProgress(null);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Share {documents.length} Document{documents.length > 1 ? 's' : ''} to Room
            </h3>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Documents Preview */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Documents to share:</h4>
            <div className="max-h-32 overflow-y-auto border border-gray-200 rounded-md">
              {documents.map((doc, index) => (
                <div key={doc.id} className={`px-3 py-2 text-sm ${index > 0 ? 'border-t border-gray-100' : ''}`}>
                  <div className="font-medium text-gray-900 truncate">{doc.title}</div>
                  <div className="text-gray-500 text-xs">{doc.file_type}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Progress Bar */}
          {shareProgress && (
            <div className="mb-6">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Sharing documents...</span>
                <span>{shareProgress.current} / {shareProgress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(shareProgress.current / shareProgress.total) * 100}%` }}
                ></div>
              </div>
              <div className="text-xs text-gray-500 mt-1 truncate">
                Current: {shareProgress.currentDocument}
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Room Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Room
              </label>
              {isLoadingRooms ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                </div>
              ) : (
                <select
                  {...register("roomId")}
                  disabled={isLoading}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                >
                  <option value="">Choose a room...</option>
                  {rooms.map((room) => (
                    <option key={room.id} value={room.id}>
                      {room.name} ({room.room_code})
                    </option>
                  ))}
                </select>
              )}
              {errors.roomId && (
                <p className="mt-1 text-sm text-red-600">{errors.roomId.message}</p>
              )}
            </div>

            {/* Permission */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Permission Level
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="download"
                    {...register("permission")}
                    disabled={isLoading}
                    className="mr-2 disabled:opacity-50"
                  />
                  <span className="text-sm">Download - Members can view and download</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="view"
                    {...register("permission")}
                    disabled={isLoading}
                    className="mr-2 disabled:opacity-50"
                  />
                  <span className="text-sm">View Only - Members can only view</span>
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || isLoadingRooms || rooms.length === 0}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? "Sharing..." : `Share ${documents.length} Document${documents.length > 1 ? 's' : ''}`}
              </button>
            </div>
          </form>

          {rooms.length === 0 && !isLoadingRooms && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                You don't belong to any rooms yet. Create or join a room first to share documents.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
