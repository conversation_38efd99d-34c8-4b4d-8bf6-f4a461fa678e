-- SmartBagPack Storage Setup
-- Run this SQL in your Supabase SQL Editor after creating the main schema

-- Create storage bucket for documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'documents',
  'documents',
  false,
  52428800, -- 50MB limit
  ARRAY[
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ]
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for documents bucket
CREATE POLICY "Authenticated users can upload documents" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can view own documents" ON storage.objects
FOR SELECT USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can view shared documents" ON storage.objects
FOR SELECT USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
  AND (
    -- Document is shared with user directly
    name IN (
      SELECT d.file_path FROM documents d
      JOIN document_shares ds ON d.id = ds.document_id
      WHERE ds.shared_with = auth.uid()
    )
    OR
    -- Document is in a room user belongs to
    name IN (
      SELECT d.file_path FROM documents d
      JOIN room_documents rd ON d.id = rd.document_id
      JOIN room_members rm ON rd.room_id = rm.room_id
      WHERE rm.user_id = auth.uid()
    )
    OR
    -- Document is public
    name IN (
      SELECT file_path FROM documents WHERE is_public = true
    )
  )
);

CREATE POLICY "Users can update own documents" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can delete own documents" ON storage.objects
FOR DELETE USING (
  bucket_id = 'documents' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Note: Storage cleanup is handled by the application layer in fileService.ts
-- Database triggers cannot directly call Supabase storage functions
-- The application handles both database deletion and storage cleanup

-- Create function to log document deletion (for audit purposes)
CREATE OR REPLACE FUNCTION log_document_deletion()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the deletion for audit purposes
  RAISE NOTICE 'Document deleted: % (file: %)', OLD.id, OLD.file_path;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to log document deletions
CREATE TRIGGER trigger_log_document_deletion
  AFTER DELETE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION log_document_deletion();

-- Create function to update document updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update updated_at timestamps
CREATE TRIGGER trigger_update_documents_updated_at
  BEFORE UPDATE ON documents
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_rooms_updated_at
  BEFORE UPDATE ON rooms
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
