-- SmartBagPack Invitation and Notification System
-- Run this SQL in your Supabase SQL Editor to add the new features

-- Create room_invitations table
CREATE TABLE IF NOT EXISTS room_invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  invited_by UUID REFERENCES auth.users(id) NOT NULL,
  invited_user UUID REFERENCES auth.users(id) NOT NULL,
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined', 'expired')) DEFAULT 'pending',
  message TEXT,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(room_id, invited_user)
);

-- Create room_invitation_links table (for shareable links)
CREATE TABLE IF NOT EXISTS room_invitation_links (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  room_id UUID REFERENCES rooms(id) ON DELETE CASCADE,
  created_by UUID REFERENCES auth.users(id) NOT NULL,
  link_token TEXT UNIQUE NOT NULL,
  max_uses INTEGER DEFAULT NULL, -- NULL means unlimited
  current_uses INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'room_invitation', 'room_join', 'room_leave', 'document_shared_to_room', 
    'document_shared_direct', 'room_document_uploaded', 'storage_warning', 
    'system_announcement', 'room_settings_changed'
  )),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}', -- Additional data like room_id, document_id, etc.
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE room_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_invitation_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for room_invitations
CREATE POLICY "Users can view invitations involving them" ON room_invitations FOR SELECT USING (
  invited_by = auth.uid() OR invited_user = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Room admins can create invitations" ON room_invitations FOR INSERT WITH CHECK (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Invited users can update invitation status" ON room_invitations FOR UPDATE USING (
  invited_user = auth.uid()
);
CREATE POLICY "Invitation creators can delete invitations" ON room_invitations FOR DELETE USING (
  invited_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);

-- RLS Policies for room_invitation_links
CREATE POLICY "Room admins can view invitation links" ON room_invitation_links FOR SELECT USING (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Room admins can create invitation links" ON room_invitation_links FOR INSERT WITH CHECK (
  room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
  OR room_id IN (SELECT room_id FROM room_members WHERE user_id = auth.uid() AND role = 'admin')
);
CREATE POLICY "Link creators can update invitation links" ON room_invitation_links FOR UPDATE USING (
  created_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);
CREATE POLICY "Link creators can delete invitation links" ON room_invitation_links FOR DELETE USING (
  created_by = auth.uid()
  OR room_id IN (SELECT id FROM rooms WHERE created_by = auth.uid())
);

-- RLS Policies for notifications
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (
  user_id = auth.uid()
);
CREATE POLICY "System can create notifications" ON notifications FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (
  user_id = auth.uid()
);
CREATE POLICY "Users can delete own notifications" ON notifications FOR DELETE USING (
  user_id = auth.uid()
);

-- Create function to generate unique invitation link tokens
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS TEXT AS $$
DECLARE
  chars TEXT := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  result TEXT := '';
  i INTEGER;
BEGIN
  FOR i IN 1..32 LOOP
    result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_room_invitations_invited_user ON room_invitations(invited_user);
CREATE INDEX IF NOT EXISTS idx_room_invitations_room_id ON room_invitations(room_id);
CREATE INDEX IF NOT EXISTS idx_room_invitations_status ON room_invitations(status);
CREATE INDEX IF NOT EXISTS idx_room_invitation_links_token ON room_invitation_links(link_token);
CREATE INDEX IF NOT EXISTS idx_room_invitation_links_room_id ON room_invitation_links(room_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);

-- Grant necessary permissions
GRANT ALL ON room_invitations TO authenticated;
GRANT ALL ON room_invitation_links TO authenticated;
GRANT ALL ON notifications TO authenticated;
