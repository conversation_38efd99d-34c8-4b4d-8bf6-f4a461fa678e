# SmartBagPack Page Titles Implementation

## Overview
This document outlines the implementation of dynamic page titles for all pages in the SmartBagPack application. Each page now has a descriptive title that follows the format "Page Name - SmartBagPack".

## Implementation Details

### Custom Hook: `usePageTitle`
Created a reusable custom hook at `src/hooks/usePageTitle.ts` that:
- Dynamically sets the document title when a component mounts
- Automatically resets the title when the component unmounts
- Accepts a page title and optional app name parameter

```typescript
export function usePageTitle(title: string, appName: string = "SmartBagPack") {
  useEffect(() => {
    document.title = `${title} - ${appName}`;
    return () => {
      document.title = appName;
    };
  }, [title, appName]);
}
```

## Page Titles Implemented

### Dashboard Pages
- **Dashboard**: "Dashboard - SmartBagPack"
- **My Documents**: "My Documents - SmartBagPack"
- **Upload Documents**: "Upload Documents - SmartBagPack"
- **Rooms**: "Rooms - SmartBagPack"
- **Shared Documents**: "Shared Documents - SmartBagPack"
- **Profile**: "Profile - SmartBagPack"

### Room Pages
- **Individual Room**: "Room: [Room Name] - SmartBagPack"
  - Dynamically updates when room data is loaded
  - Falls back to "Room - SmartBagPack" while loading

### Authentication Pages
- **Login**: "Login - SmartBagPack"
- **Sign Up**: "Sign Up - SmartBagPack"
- **Reset Password**: "Reset Password - SmartBagPack"

### Default Title
- **Base HTML**: "SmartBagPack" (updated in index.html)

## Files Modified

### Core Hook
- `src/hooks/usePageTitle.ts` - New custom hook for managing page titles

### Dashboard Pages
- `src/components/Dashboard.tsx` - Added "Dashboard" title
- `src/pages/Documents.tsx` - Added "My Documents" title
- `src/pages/Upload.tsx` - Added "Upload Documents" title
- `src/pages/Rooms.tsx` - Added "Rooms" title
- `src/pages/RoomDetail.tsx` - Added dynamic "Room: [Name]" title
- `src/pages/SharedDocuments.tsx` - Added "Shared Documents" title
- `src/pages/Profile.tsx` - Added "Profile" title

### Authentication Pages
- `src/components/auth/LoginForm.tsx` - Added "Login" title
- `src/components/auth/RegisterForm.tsx` - Added "Sign Up" title
- `src/components/auth/ForgotPasswordForm.tsx` - Added "Reset Password" title

### Base HTML
- `index.html` - Updated default title from "Vite + React + TS" to "SmartBagPack"

## Benefits

### User Experience
- **Tab Identification**: Users can easily identify which page they're viewing when multiple tabs are open
- **Browser History**: Page titles appear in browser history for better navigation
- **Bookmarks**: Meaningful titles when users bookmark pages

### SEO Benefits
- **Search Engine Optimization**: Descriptive titles improve search engine indexing
- **Social Sharing**: Better titles when pages are shared on social media
- **Accessibility**: Screen readers can announce page titles to visually impaired users

### Developer Benefits
- **Consistent Format**: All titles follow the same "Page Name - SmartBagPack" pattern
- **Maintainable**: Centralized hook makes it easy to update title format across the app
- **Dynamic Updates**: Titles automatically update when page content changes (e.g., room names)

## Usage Examples

### Static Page Title
```typescript
export function MyPage() {
  usePageTitle("My Page");
  // Component content...
}
```

### Dynamic Page Title
```typescript
export function RoomDetail() {
  const [room, setRoom] = useState(null);
  usePageTitle(room ? `Room: ${room.name}` : "Room");
  // Component content...
}
```

## Testing
All page titles have been implemented and tested with hot module reloading. The titles update correctly when:
- Navigating between pages
- Loading dynamic content (like room names)
- Refreshing pages
- Opening multiple tabs

The implementation is fully functional and ready for production use.
