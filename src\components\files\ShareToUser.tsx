import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import type { DocumentFile } from "../../lib/fileService";
import { fileService } from "../../lib/fileService";
import { userService } from "../../lib/userService";
import type { UserSearchResult } from "../../lib/userService";
import { UserSearch } from "../shared/UserSearch";
import { useAuth } from "../../contexts/AuthContext";

const shareSchema = z.object({
  permission: z.enum(["view", "download"]),
  expiresAt: z.string().optional(),
});

type ShareFormData = z.infer<typeof shareSchema>;

interface ShareToUserProps {
  document: DocumentFile;
  onClose: () => void;
  onShareComplete?: () => void;
}

export function ShareToUser({
  document,
  onClose,
  onShareComplete,
}: ShareToUserProps) {
  const { user } = useAuth();
  const [selectedUsers, setSelectedUsers] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { register, handleSubmit } = useForm<ShareFormData>({
    defaultValues: {
      permission: "download",
    },
  });

  const handleUserSelect = (selectedUser: UserSearchResult) => {
    if (!selectedUsers.find((u) => u.id === selectedUser.id)) {
      setSelectedUsers((prev) => [...prev, selectedUser]);
    }
  };

  const removeUser = (userId: string) => {
    setSelectedUsers((prev) => prev.filter((u) => u.id !== userId));
  };

  const onSubmit = async (data: ShareFormData) => {
    if (!user) return;

    if (selectedUsers.length === 0) {
      toast.error("Please select at least one user to share with");
      return;
    }

    try {
      setIsLoading(true);

      // Share with each selected user
      const sharePromises = selectedUsers.map((selectedUser) =>
        fileService.shareDocumentWithUser(
          document.id,
          selectedUser.id,
          user.id,
          data.permission,
          data.expiresAt || undefined
        )
      );

      await Promise.all(sharePromises);

      const userNames = selectedUsers.map((u) =>
        userService.getUserDisplayName(u)
      );
      const message =
        selectedUsers.length === 1
          ? `Document shared with ${userNames[0]} successfully!`
          : `Document shared with ${selectedUsers.length} users successfully!`;

      toast.success(message);
      onShareComplete?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to share document");
    } finally {
      setIsLoading(false);
    }
  };

  const getUserInitials = (user: UserSearchResult): string => {
    return userService.getUserInitials(user);
  };

  const getUserDisplayName = (user: UserSearchResult): string => {
    return userService.getUserDisplayName(user);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Share with Users
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Document info */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-1">{document.title}</h3>
            <p className="text-sm text-gray-600">
              {document.file_type.toUpperCase()} •{" "}
              {Math.round(document.file_size / 1024)} KB
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* User Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search and select users
              </label>
              <UserSearch
                onUserSelect={handleUserSelect}
                selectedUsers={selectedUsers}
                placeholder="Search users by name or email..."
                excludeUserIds={user ? [user.id] : []}
              />
            </div>

            {/* Selected Users */}
            {selectedUsers.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selected users ({selectedUsers.length})
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {selectedUsers.map((selectedUser) => (
                    <div
                      key={selectedUser.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium text-sm">
                            {getUserInitials(selectedUser)}
                          </span>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {getUserDisplayName(selectedUser)}
                          </p>
                          <p className="text-xs text-gray-500">
                            {selectedUser.email}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeUser(selectedUser.id)}
                        className="text-red-500 hover:text-red-700 p-1"
                        title="Remove user"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Permission Settings */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Permission Level
              </label>
              <div className="space-y-3">
                <label className="flex items-start space-x-3 cursor-pointer">
                  <input
                    {...register("permission")}
                    type="radio"
                    value="view"
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      View Only
                    </div>
                    <div className="text-sm text-gray-500">
                      Users can view the document but cannot download it
                    </div>
                  </div>
                </label>
                <label className="flex items-start space-x-3 cursor-pointer">
                  <input
                    {...register("permission")}
                    type="radio"
                    value="download"
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      Download
                    </div>
                    <div className="text-sm text-gray-500">
                      Users can view and download the document
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Expiration Date (Optional) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiration Date (Optional)
              </label>
              <input
                {...register("expiresAt")}
                type="datetime-local"
                className="input-field"
                min={new Date().toISOString().slice(0, 16)}
              />
              <p className="mt-1 text-sm text-gray-500">
                Leave empty for permanent access
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || selectedUsers.length === 0}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading
                  ? "Sharing..."
                  : `Share with ${selectedUsers.length} user${
                      selectedUsers.length !== 1 ? "s" : ""
                    }`}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
