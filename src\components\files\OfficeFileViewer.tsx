import { useState, useEffect, useRef } from "react";
// import <PERSON>Viewer from 'react-file-viewer'; // Temporarily disabled due to compatibility issues

interface OfficeFileViewerProps {
  fileUrl: string;
  fileName: string;
  fileType: string;
  mimeType: string;
  onError?: (error: string) => void;
}

export function OfficeFileViewer({
  fileUrl,
  fileName,
  fileType,
  mimeType,
  onError,
}: OfficeFileViewerProps) {
  const [viewerMethod, setViewerMethod] = useState<"google-docs" | "error">(
    "google-docs"
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [loadTimeout, setLoadTimeout] = useState<NodeJS.Timeout | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Check if file is Excel
  const isExcelFile = (mimeType: string): boolean => {
    return [
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ].includes(mimeType);
  };

  // Create appropriate viewer URL based on file type
  const getViewerUrl = (fileUrl: string, mimeType: string): string => {
    const encodedUrl = encodeURIComponent(fileUrl);

    if (isExcelFile(mimeType)) {
      // Use Office Online viewer for Excel files (more reliable than Google Sheets)
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
    } else {
      // Use Google Docs viewer for Word and PowerPoint files
      return `https://docs.google.com/gview?url=${encodedUrl}&embedded=true`;
    }
  };

  const handleGoogleDocsError = () => {
    const viewerName = isExcelFile(mimeType)
      ? "Office Online viewer"
      : "Google Docs viewer";
    const errorMessage = `Unable to preview this ${fileType} file. ${viewerName} failed to load the document.`;
    setError(errorMessage);
    setViewerMethod("error");
    setIsLoading(false);
    onError?.(errorMessage);
  };

  const handleGoogleDocsLoad = () => {
    setIsLoading(false);
    setError("");
    if (loadTimeout) {
      clearTimeout(loadTimeout);
      setLoadTimeout(null);
    }
  };

  useEffect(() => {
    // Suppress Office Online deprecation warnings and disposal errors
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;

    const suppressOfficeErrors = (originalMethod: typeof console.warn) => {
      return (...args: any[]) => {
        const message = args[0]?.toString() || "";
        // Suppress known Office Online issues
        if (
          message.includes("DOMNodeInserted") ||
          message.includes("mutation event") ||
          message.includes("officeonline") ||
          message.includes("office.net") ||
          message.includes("Cannot read properties of null")
        ) {
          return; // Suppress these specific errors
        }
        originalMethod.apply(console, args);
      };
    };

    console.warn = suppressOfficeErrors(originalConsoleWarn);
    console.error = suppressOfficeErrors(originalConsoleError);

    // Reset state when file changes
    setViewerMethod("google-docs");
    setIsLoading(true);
    setError("");

    // Clear any existing timeout
    if (loadTimeout) {
      clearTimeout(loadTimeout);
    }

    // Set a timeout to detect if the viewer fails to load
    const timeout = setTimeout(() => {
      setIsLoading((prevLoading) => {
        if (prevLoading) {
          const viewerName = isExcelFile(mimeType)
            ? "Office Online viewer"
            : "Google Docs viewer";
          const errorMessage = `${viewerName} is taking too long to load. This may be due to network issues or the file format not being supported.`;
          setError(errorMessage);
          setViewerMethod("error");
          onError?.(errorMessage);
          return false;
        }
        return prevLoading;
      });
    }, 15000); // 15 second timeout

    setLoadTimeout(timeout);

    return () => {
      // Restore original console methods
      console.warn = originalConsoleWarn;
      console.error = originalConsoleError;

      if (timeout) {
        clearTimeout(timeout);
      }

      // Clean up iframe to prevent Office Online disposal errors
      if (iframeRef.current) {
        try {
          // Try to gracefully unload the iframe content
          iframeRef.current.src = "about:blank";
        } catch (error) {
          // Suppress any cleanup errors from Office Online
        }
      }
    };
  }, [fileUrl, mimeType, onError]);

  const renderViewer = () => {
    switch (viewerMethod) {
      case "google-docs":
        return (
          <div className="h-full w-full relative">
            {isLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-4 text-gray-600">
                    Loading with Google Docs viewer...
                  </p>
                </div>
              </div>
            )}
            <iframe
              ref={iframeRef}
              src={getViewerUrl(fileUrl, mimeType)}
              className="w-full h-full border-0"
              title={fileName}
              onLoad={handleGoogleDocsLoad}
              onError={handleGoogleDocsError}
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation"
            />
          </div>
        );

      case "error":
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md mx-auto p-6">
              <div className="text-red-500 text-6xl mb-4">
                {fileType === "Word Document"
                  ? "📝"
                  : fileType === "Presentation"
                  ? "📽️"
                  : fileType === "Spreadsheet"
                  ? "📊"
                  : "📄"}
              </div>
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                Preview Unavailable
              </h4>
              <p className="text-gray-600 mb-4">{error}</p>
              <div className="bg-gray-50 rounded-lg p-4 mb-4 text-sm text-gray-600">
                <p className="font-medium mb-2">Preview method attempted:</p>
                <ul className="list-disc list-inside space-y-1 text-left">
                  <li>
                    {isExcelFile(mimeType)
                      ? "Office Online Viewer"
                      : "Google Docs Viewer"}{" "}
                    - Failed
                  </li>
                </ul>
              </div>
              <p className="text-sm text-gray-500">
                Download the file to view it in{" "}
                {fileType === "Word Document"
                  ? "Microsoft Word"
                  : fileType === "Presentation"
                  ? "Microsoft PowerPoint"
                  : fileType === "Spreadsheet"
                  ? "Microsoft Excel"
                  : "the appropriate application"}
                .
              </p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="h-full w-full relative bg-gray-50">
      {/* Viewer Method Indicator */}
      {!error && (
        <div className="absolute top-2 right-2 z-20">
          <div className="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
            {isExcelFile(mimeType)
              ? "Office Online Viewer"
              : "Google Docs Viewer"}
          </div>
        </div>
      )}

      {renderViewer()}
    </div>
  );
}
