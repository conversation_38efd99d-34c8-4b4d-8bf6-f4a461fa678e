import { useState } from "react";
import {
  XMarkIcon,
  ShareIcon,
  UserGroupIcon,
  LinkIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "../../contexts/AuthContext";
import type { Folder } from "../../lib/folderService";
import toast from "react-hot-toast";

interface FolderShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  folder: Folder | null;
  onSuccess?: () => void;
}

export function FolderShareModal({
  isOpen,
  onClose,
  folder,
  onSuccess,
}: FolderShareModalProps) {
  const { user } = useAuth();
  const [shareMethod, setShareMethod] = useState<"link" | "users">("link");
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [shareLink, setShareLink] = useState<string | null>(null);

  const generateShareLink = async () => {
    if (!user || !folder) return;

    setIsGeneratingLink(true);
    try {
      // TODO: Implement actual share link generation
      // For now, create a mock share link
      const mockLink = `${window.location.origin}/shared/folder/${
        folder.id
      }?token=${Date.now()}`;
      setShareLink(mockLink);
      toast.success("Share link generated successfully");
    } catch (error: any) {
      console.error("Failed to generate share link:", error);
      toast.error("Failed to generate share link");
    } finally {
      setIsGeneratingLink(false);
    }
  };

  const copyToClipboard = async () => {
    if (!shareLink) return;

    try {
      await navigator.clipboard.writeText(shareLink);
      toast.success("Link copied to clipboard");
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
      toast.error("Failed to copy link");
    }
  };

  if (!isOpen || !folder) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <ShareIcon className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-medium text-gray-900">
                Share Folder
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Share "{folder.name}" with others
            </p>
          </div>

          {/* Share Method Tabs */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6">
            <button
              onClick={() => setShareMethod("link")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                shareMethod === "link"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <LinkIcon className="h-4 w-4 inline mr-2" />
              Share Link
            </button>
            <button
              onClick={() => setShareMethod("users")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                shareMethod === "users"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <UserGroupIcon className="h-4 w-4 inline mr-2" />
              Share with Users
            </button>
          </div>

          {shareMethod === "link" ? (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <LinkIcon className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-blue-900">
                      Share via Link
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      Anyone with this link will be able to view the folder and
                      its contents.
                    </p>
                  </div>
                </div>
              </div>

              {shareLink ? (
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={shareLink}
                      readOnly
                      className="flex-1 input-field bg-gray-50"
                    />
                    <button
                      onClick={copyToClipboard}
                      className="btn-secondary whitespace-nowrap"
                    >
                      Copy Link
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    This link will remain active until you revoke access.
                  </p>
                </div>
              ) : (
                <button
                  onClick={generateShareLink}
                  className="w-full btn-primary"
                  disabled={isGeneratingLink}
                >
                  {isGeneratingLink ? "Generating..." : "Generate Share Link"}
                </button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <UserGroupIcon className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-amber-900">
                      Coming Soon
                    </h4>
                    <p className="text-sm text-amber-700 mt-1">
                      Direct user sharing for folders will be available in a
                      future update. For now, you can share individual documents
                      within the folder.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 mt-6">
            <button onClick={onClose} className="btn-secondary">
              Close
            </button>
            {shareMethod === "link" && shareLink && (
              <button
                onClick={() => {
                  onSuccess?.();
                  onClose();
                }}
                className="btn-primary"
              >
                Done
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
