-- Enhanced Folder Management Migration
-- Run this SQL in your Supabase SQL Editor to add enhanced folder management features

-- Add indexes for better performance on folder operations
CREATE INDEX IF NOT EXISTS idx_documents_user_folder ON documents(user_id, folder_id);
CREATE INDEX IF NOT EXISTS idx_documents_file_size ON documents(file_size);
CREATE INDEX IF NOT EXISTS idx_folders_user_parent ON folders(user_id, parent_folder_id);

-- Add a function to calculate folder size recursively
CREATE OR REPLACE FUNCTION get_folder_size_recursive(folder_uuid UUID, user_uuid UUID)
RETURNS BIGINT AS $$
DECLARE
    total_size BIGINT := 0;
    subfolder_size BIGINT := 0;
    subfolder_record RECORD;
BEGIN
    -- Get direct documents size in this folder
    SELECT COALESCE(SUM(file_size), 0) INTO total_size
    FROM documents 
    WHERE folder_id = folder_uuid AND user_id = user_uuid;
    
    -- Get size from all subfolders recursively
    FOR subfolder_record IN 
        SELECT id FROM folders 
        WHERE parent_folder_id = folder_uuid AND user_id = user_uuid
    LOOP
        SELECT get_folder_size_recursive(subfolder_record.id, user_uuid) INTO subfolder_size;
        total_size := total_size + subfolder_size;
    END LOOP;
    
    RETURN total_size;
END;
$$ LANGUAGE plpgsql;

-- Add a function to count documents in folder recursively
CREATE OR REPLACE FUNCTION get_folder_document_count_recursive(folder_uuid UUID, user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    total_count INTEGER := 0;
    subfolder_count INTEGER := 0;
    subfolder_record RECORD;
BEGIN
    -- Get direct documents count in this folder
    SELECT COALESCE(COUNT(*), 0) INTO total_count
    FROM documents 
    WHERE folder_id = folder_uuid AND user_id = user_uuid;
    
    -- Get count from all subfolders recursively
    FOR subfolder_record IN 
        SELECT id FROM folders 
        WHERE parent_folder_id = folder_uuid AND user_id = user_uuid
    LOOP
        SELECT get_folder_document_count_recursive(subfolder_record.id, user_uuid) INTO subfolder_count;
        total_count := total_count + subfolder_count;
    END LOOP;
    
    RETURN total_count;
END;
$$ LANGUAGE plpgsql;

-- Add a function to prevent circular folder references
CREATE OR REPLACE FUNCTION check_folder_circular_reference()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if the new parent would create a circular reference
    IF NEW.parent_folder_id IS NOT NULL THEN
        -- Check if the parent is the same as the folder itself
        IF NEW.parent_folder_id = NEW.id THEN
            RAISE EXCEPTION 'Cannot set folder as its own parent';
        END IF;
        
        -- Check if the parent is a descendant of this folder
        IF EXISTS (
            WITH RECURSIVE folder_tree AS (
                -- Start with the new parent
                SELECT id, parent_folder_id, path
                FROM folders 
                WHERE id = NEW.parent_folder_id AND user_id = NEW.user_id
                
                UNION ALL
                
                -- Recursively find all descendants
                SELECT f.id, f.parent_folder_id, f.path
                FROM folders f
                INNER JOIN folder_tree ft ON f.parent_folder_id = ft.id
                WHERE f.user_id = NEW.user_id
            )
            SELECT 1 FROM folder_tree WHERE id = NEW.id
        ) THEN
            RAISE EXCEPTION 'Cannot create circular folder reference';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for circular reference check
DROP TRIGGER IF EXISTS check_folder_circular_reference_trigger ON folders;
CREATE TRIGGER check_folder_circular_reference_trigger
    BEFORE INSERT OR UPDATE ON folders
    FOR EACH ROW
    EXECUTE FUNCTION check_folder_circular_reference();

-- Add a function to validate folder operations
CREATE OR REPLACE FUNCTION validate_folder_operation()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure folder name is not empty
    IF NEW.name IS NULL OR TRIM(NEW.name) = '' THEN
        RAISE EXCEPTION 'Folder name cannot be empty';
    END IF;
    
    -- Ensure folder name doesn't contain invalid characters
    IF NEW.name ~ '[<>:"/\\|?*]' THEN
        RAISE EXCEPTION 'Folder name contains invalid characters';
    END IF;
    
    -- Limit folder name length
    IF LENGTH(NEW.name) > 255 THEN
        RAISE EXCEPTION 'Folder name is too long (maximum 255 characters)';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for folder validation
DROP TRIGGER IF EXISTS validate_folder_operation_trigger ON folders;
CREATE TRIGGER validate_folder_operation_trigger
    BEFORE INSERT OR UPDATE ON folders
    FOR EACH ROW
    EXECUTE FUNCTION validate_folder_operation();

-- Add a view for folder statistics
CREATE OR REPLACE VIEW folder_stats AS
SELECT 
    f.id,
    f.user_id,
    f.name,
    f.parent_folder_id,
    f.path,
    f.created_at,
    f.updated_at,
    COALESCE(doc_stats.document_count, 0) as direct_document_count,
    COALESCE(doc_stats.total_size, 0) as direct_size,
    COALESCE(subfolder_stats.subfolder_count, 0) as subfolder_count,
    get_folder_document_count_recursive(f.id, f.user_id) as total_document_count,
    get_folder_size_recursive(f.id, f.user_id) as total_size
FROM folders f
LEFT JOIN (
    SELECT 
        folder_id,
        COUNT(*) as document_count,
        SUM(file_size) as total_size
    FROM documents 
    GROUP BY folder_id
) doc_stats ON f.id = doc_stats.folder_id
LEFT JOIN (
    SELECT 
        parent_folder_id,
        COUNT(*) as subfolder_count
    FROM folders 
    WHERE parent_folder_id IS NOT NULL
    GROUP BY parent_folder_id
) subfolder_stats ON f.id = subfolder_stats.parent_folder_id;

-- Add RLS policies for the new functions and views
ALTER VIEW folder_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own folder stats" ON folder_stats
FOR SELECT USING (auth.uid() = user_id);

-- Add a function to clean up empty folders
CREATE OR REPLACE FUNCTION cleanup_empty_folders(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    folder_record RECORD;
BEGIN
    -- Find empty folders (no documents and no subfolders)
    FOR folder_record IN 
        SELECT f.id 
        FROM folders f
        WHERE f.user_id = user_uuid
        AND NOT EXISTS (
            SELECT 1 FROM documents d WHERE d.folder_id = f.id
        )
        AND NOT EXISTS (
            SELECT 1 FROM folders sf WHERE sf.parent_folder_id = f.id
        )
    LOOP
        DELETE FROM folders WHERE id = folder_record.id;
        deleted_count := deleted_count + 1;
    END LOOP;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Add a function to get folder breadcrumb path
CREATE OR REPLACE FUNCTION get_folder_breadcrumb(folder_uuid UUID, user_uuid UUID)
RETURNS TABLE(id UUID, name TEXT, level INTEGER) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE folder_path AS (
        -- Start with the target folder
        SELECT f.id, f.name, f.parent_folder_id, 0 as level
        FROM folders f
        WHERE f.id = folder_uuid AND f.user_id = user_uuid
        
        UNION ALL
        
        -- Recursively get parent folders
        SELECT f.id, f.name, f.parent_folder_id, fp.level + 1
        FROM folders f
        INNER JOIN folder_path fp ON f.id = fp.parent_folder_id
        WHERE f.user_id = user_uuid
    )
    SELECT fp.id, fp.name, fp.level
    FROM folder_path fp
    ORDER BY fp.level DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_folder_size_recursive(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_folder_document_count_recursive(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_empty_folders(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_folder_breadcrumb(UUID, UUID) TO authenticated;
GRANT SELECT ON folder_stats TO authenticated;

-- Add comments for documentation
COMMENT ON FUNCTION get_folder_size_recursive(UUID, UUID) IS 'Calculate total size of folder including all subfolders recursively';
COMMENT ON FUNCTION get_folder_document_count_recursive(UUID, UUID) IS 'Count total documents in folder including all subfolders recursively';
COMMENT ON FUNCTION cleanup_empty_folders(UUID) IS 'Remove empty folders for a user';
COMMENT ON FUNCTION get_folder_breadcrumb(UUID, UUID) IS 'Get breadcrumb path for a folder';
COMMENT ON VIEW folder_stats IS 'Comprehensive folder statistics including recursive counts';

-- Create indexes for the new functions
CREATE INDEX IF NOT EXISTS idx_folders_recursive_lookup ON folders(user_id, parent_folder_id, id);
CREATE INDEX IF NOT EXISTS idx_documents_folder_lookup ON documents(folder_id, user_id, file_size);
