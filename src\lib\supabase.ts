import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          role: "student" | "lecturer" | "admin";
          institution: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          role?: "student" | "lecturer" | "admin";
          institution?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          role?: "student" | "lecturer" | "admin";
          institution?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      documents: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          file_path: string;
          file_size: number;
          file_type: string;
          mime_type: string;
          is_public: boolean;
          download_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          description?: string | null;
          file_path: string;
          file_size: number;
          file_type: string;
          mime_type: string;
          is_public?: boolean;
          download_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          description?: string | null;
          file_path?: string;
          file_size?: number;
          file_type?: string;
          mime_type?: string;
          is_public?: boolean;
          download_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      rooms: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          room_code: string;
          created_by: string;
          is_private: boolean;
          max_members: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          room_code: string;
          created_by: string;
          is_private?: boolean;
          max_members?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          room_code?: string;
          created_by?: string;
          is_private?: boolean;
          max_members?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      room_members: {
        Row: {
          id: string;
          room_id: string;
          user_id: string;
          role: "admin" | "member";
          joined_at: string;
        };
        Insert: {
          id?: string;
          room_id: string;
          user_id: string;
          role?: "admin" | "member";
          joined_at?: string;
        };
        Update: {
          id?: string;
          room_id?: string;
          user_id?: string;
          role?: "admin" | "member";
          joined_at?: string;
        };
      };
      room_documents: {
        Row: {
          id: string;
          room_id: string;
          document_id: string;
          shared_by: string;
          permission: "view" | "download";
          shared_at: string;
        };
        Insert: {
          id?: string;
          room_id: string;
          document_id: string;
          shared_by: string;
          permission?: "view" | "download";
          shared_at?: string;
        };
        Update: {
          id?: string;
          room_id?: string;
          document_id?: string;
          shared_by?: string;
          permission?: "view" | "download";
          shared_at?: string;
        };
      };
      document_shares: {
        Row: {
          id: string;
          document_id: string;
          shared_by: string;
          shared_with: string;
          permission: "view" | "download";
          expires_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          document_id: string;
          shared_by: string;
          shared_with: string;
          permission?: "view" | "download";
          expires_at?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          document_id?: string;
          shared_by?: string;
          shared_with?: string;
          permission?: "view" | "download";
          expires_at?: string | null;
          created_at?: string;
        };
      };
      room_invitations: {
        Row: {
          id: string;
          room_id: string;
          invited_by: string;
          invited_user: string;
          status: "pending" | "accepted" | "declined" | "expired";
          message: string | null;
          expires_at: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          room_id: string;
          invited_by: string;
          invited_user: string;
          status?: "pending" | "accepted" | "declined" | "expired";
          message?: string | null;
          expires_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          room_id?: string;
          invited_by?: string;
          invited_user?: string;
          status?: "pending" | "accepted" | "declined" | "expired";
          message?: string | null;
          expires_at?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      room_invitation_links: {
        Row: {
          id: string;
          room_id: string;
          created_by: string;
          link_token: string;
          max_uses: number | null;
          current_uses: number;
          expires_at: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          room_id: string;
          created_by: string;
          link_token: string;
          max_uses?: number | null;
          current_uses?: number;
          expires_at?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          room_id?: string;
          created_by?: string;
          link_token?: string;
          max_uses?: number | null;
          current_uses?: number;
          expires_at?: string;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      notifications: {
        Row: {
          id: string;
          user_id: string;
          type:
            | "room_invitation"
            | "room_join"
            | "room_leave"
            | "document_shared_to_room"
            | "document_shared_direct"
            | "room_document_uploaded"
            | "storage_warning"
            | "system_announcement"
            | "room_settings_changed";
          title: string;
          message: string;
          data: Record<string, any>;
          is_read: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          type:
            | "room_invitation"
            | "room_join"
            | "room_leave"
            | "document_shared_to_room"
            | "document_shared_direct"
            | "room_document_uploaded"
            | "storage_warning"
            | "system_announcement"
            | "room_settings_changed";
          title: string;
          message: string;
          data?: Record<string, any>;
          is_read?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          type?:
            | "room_invitation"
            | "room_join"
            | "room_leave"
            | "document_shared_to_room"
            | "document_shared_direct"
            | "room_document_uploaded"
            | "storage_warning"
            | "system_announcement"
            | "room_settings_changed";
          title?: string;
          message?: string;
          data?: Record<string, any>;
          is_read?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}
