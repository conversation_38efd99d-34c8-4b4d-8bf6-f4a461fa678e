import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { z } from "zod";
import toast from "react-hot-toast";
import { roomService } from "../../lib/roomService";
import type { RoomWithDetails } from "../../lib/roomService";

const joinRoomSchema = z.object({
  roomCode: z
    .string()
    .min(1, "Room code is required")
    .max(10, "Room code too long"),
});

type JoinRoomFormData = z.infer<typeof joinRoomSchema>;

interface JoinRoomModalProps {
  onClose: () => void;
  onRoomJoined: () => void;
  userId: string;
}

export function JoinRoomModal({
  onClose,
  onRoomJoined,
  userId,
}: JoinRoomModalProps) {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<RoomWithDetails[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [joinMode, setJoinMode] = useState<"code" | "search">("code");

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<JoinRoomFormData>({
    defaultValues: {
      roomCode: "",
    },
  });

  const roomCode = watch("roomCode");

  // Search for rooms
  const searchRooms = async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await roomService.searchRooms(query, userId);
      setSearchResults(results);
    } catch (error: any) {
      console.error("Search rooms error:", error);
      toast.error("Failed to search rooms");
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    if (query.length >= 2) {
      searchRooms(query);
    } else {
      setSearchResults([]);
    }
  };

  // Join room by room object
  const joinRoomByObject = async (room: RoomWithDetails) => {
    try {
      setIsLoading(true);

      // Check if user is already a member
      if (room.is_member) {
        toast.error("You are already a member of this room");
        setIsLoading(false);
        return;
      }

      // If room is private, prompt for room code
      if (room.is_private) {
        const enteredCode = prompt(
          `"${room.name}" is a private room. Please enter the room code to join:`
        );

        if (!enteredCode) {
          setIsLoading(false);
          return;
        }

        if (enteredCode.toUpperCase() !== room.room_code.toUpperCase()) {
          toast.error("Incorrect room code. Please try again.");
          setIsLoading(false);
          return;
        }
      }

      await roomService.joinRoom(room.room_code, userId);
      toast.success(`Successfully joined "${room.name}"!`);
      onRoomJoined();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to join room");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: JoinRoomFormData) => {
    try {
      setIsLoading(true);
      const cleanRoomCode = data.roomCode.toUpperCase().trim();
      await roomService.joinRoom(cleanRoomCode, userId);
      toast.success("Successfully joined the room!");
      onRoomJoined();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to join room");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Join Room</h3>
            <button
              onClick={onClose}
              disabled={isLoading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Join Mode Tabs */}
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6">
            <button
              onClick={() => setJoinMode("code")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                joinMode === "code"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Join by Code
            </button>
            <button
              onClick={() => setJoinMode("search")}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                joinMode === "search"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              Search Rooms
            </button>
          </div>

          {joinMode === "code" ? (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Room Code Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Room Code *
                </label>
                <input
                  type="text"
                  {...register("roomCode")}
                  disabled={isLoading}
                  placeholder="Enter 6-character room code"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 font-mono text-center text-lg tracking-wider uppercase"
                  maxLength={10}
                  style={{ textTransform: "uppercase" }}
                />
                {errors.roomCode && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.roomCode.message}
                  </p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  Room codes are usually 6 characters long (e.g., ABC123)
                </p>
              </div>

              {/* Preview */}
              {roomCode && roomCode.length > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <svg
                      className="w-4 h-4 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    <span className="text-sm text-gray-700">
                      Looking for room:{" "}
                      <span className="font-mono font-medium">
                        {roomCode.toUpperCase()}
                      </span>
                    </span>
                  </div>
                </div>
              )}

              {/* Info Box */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <svg
                    className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <div className="text-xs text-blue-700">
                    <p className="font-medium mb-1">How to get a room code:</p>
                    <ul className="space-y-1">
                      <li>• Ask the room creator or admin for the code</li>
                      <li>• Room codes are case-insensitive</li>
                      <li>• You'll become a member once you join</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading || !roomCode || roomCode.length === 0}
                  className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isLoading && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  )}
                  <span>{isLoading ? "Joining..." : "Join Room"}</span>
                </button>
              </div>
            </form>
          ) : (
            /* Search Mode */
            <div className="space-y-4">
              {/* Search Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search for rooms
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  disabled={isLoading}
                  placeholder="Search by room name or code..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Type at least 2 characters to search
                </p>
              </div>

              {/* Search Results */}
              {isSearching ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Searching...</span>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {searchResults.map((room) => (
                    <div
                      key={room.id}
                      className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {room.name}
                          </h4>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                            <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                              {room.room_code}
                            </span>
                            <span>{room.member_count} members</span>
                            <span>{room.document_count} documents</span>
                            {room.is_private && (
                              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                                Private
                              </span>
                            )}
                          </div>
                          {room.description && (
                            <p className="text-xs text-gray-600 mt-1 truncate">
                              {room.description}
                            </p>
                          )}
                        </div>
                        {room.is_member ? (
                          <div className="ml-3 flex items-center space-x-2">
                            <span className="px-3 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-md">
                              {room.user_role === "admin" ? "Owner" : "Member"}
                            </span>
                            <button
                              onClick={() => {
                                onClose();
                                // Navigate to room detail page
                                navigate(`/rooms/${room.id}`);
                              }}
                              className="px-3 py-1 text-xs font-medium bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            >
                              View
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => joinRoomByObject(room)}
                            disabled={isLoading}
                            className={`ml-3 px-3 py-1 text-xs font-medium rounded-md ${
                              room.is_private
                                ? "bg-yellow-600 text-white hover:bg-yellow-700 disabled:opacity-50"
                                : "bg-green-600 text-white hover:bg-green-700 disabled:opacity-50"
                            }`}
                          >
                            {room.is_private ? "Join Private" : "Join"}
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchQuery.length >= 2 ? (
                <div className="text-center py-8 text-gray-500">
                  <svg
                    className="w-12 h-12 mx-auto mb-4 text-gray-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                  <p>No rooms found matching "{searchQuery}"</p>
                  <p className="text-xs mt-1">Try a different search term</p>
                </div>
              ) : null}
            </div>
          )}

          {/* Alternative Actions */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center mb-3">
              Don't have a room code?
            </p>
            <button
              onClick={() => {
                onClose();
                // This would trigger the create room modal
                // We'll handle this in the parent component
              }}
              className="w-full text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Create your own room instead
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
