import { useEffect } from 'react';

/**
 * Custom hook to set the page title dynamically
 * @param title - The page title (without the app name)
 * @param appName - The application name (defaults to "SmartBagPack")
 */
export function usePageTitle(title: string, appName: string = "SmartBagPack") {
  useEffect(() => {
    // Set the document title
    document.title = `${title} - ${appName}`;
    
    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = appName;
    };
  }, [title, appName]);
}

/**
 * Utility function to set page title immediately (for use outside of React components)
 * @param title - The page title (without the app name)
 * @param appName - The application name (defaults to "SmartBagPack")
 */
export function setPageTitle(title: string, appName: string = "SmartBagPack") {
  document.title = `${title} - ${appName}`;
}
