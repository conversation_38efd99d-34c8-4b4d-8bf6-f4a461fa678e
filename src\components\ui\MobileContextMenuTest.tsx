import { useIsMobile } from "./ThreeDotMenu";

/**
 * Test component to verify mobile context menu functionality
 * This component can be temporarily added to any page to test mobile interactions
 */
export function MobileContextMenuTest() {
  const isMobile = useIsMobile();

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-xs">
      <h3 className="font-semibold text-sm mb-2">Mobile Context Menu Test</h3>
      <div className="space-y-2 text-xs">
        <div>
          <span className="font-medium">Device Type:</span>{" "}
          <span className={isMobile ? "text-green-600" : "text-blue-600"}>
            {isMobile ? "Mobile/Touch" : "Desktop"}
          </span>
        </div>
        <div>
          <span className="font-medium">Screen Width:</span> {window.innerWidth}
          px
        </div>
        <div>
          <span className="font-medium">Touch Support:</span>{" "}
          <span
            className={
              "ontouchstart" in window ? "text-green-600" : "text-red-600"
            }
          >
            {"ontouchstart" in window ? "Yes" : "No"}
          </span>
        </div>
        <div>
          <span className="font-medium">Max Touch Points:</span>{" "}
          {navigator.maxTouchPoints}
        </div>
        <div className="pt-2 border-t border-gray-200">
          <p className="text-gray-600">
            {isMobile
              ? "✓ Three-dot menus should be visible"
              : "✓ Right-click context menus should work"}
          </p>
        </div>
      </div>
    </div>
  );
}

// Instructions for testing:
/*
To test the mobile context menu system:

1. Add <MobileContextMenuTest /> to any page temporarily
2. Test on different devices:
   - Desktop: Should show "Desktop" and right-click should work
   - Mobile: Should show "Mobile/Touch" and three-dot menus should be visible
   - Tablet: Should show "Mobile/Touch" and three-dot menus should work

3. Test interactions:
   - On mobile: Tap three-dot menus to open action modals
   - On desktop: Right-click items to open context menus
   - Verify drag-and-drop still works on desktop
   - Verify touch gestures work properly on mobile

4. Test edge cases:
   - Resize browser window to trigger mobile/desktop switch
   - Test on devices with both touch and mouse (like Surface)
   - Test context menu positioning near screen edges

5. Remove the test component when done
*/
