import React, { useState, useEffect, useRef } from "react";
import {
  DocumentDuplicateIcon,
  ArrowRightIcon,
  TrashIcon,
  PencilIcon,
  ShareIcon,
  FolderPlusIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from "@heroicons/react/24/outline";

export interface ContextMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  disabled?: boolean;
  destructive?: boolean;
  divider?: boolean;
}

interface ContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number };
  items: ContextMenuItem[];
  onClose: () => void;
}

export function ContextMenu({
  isOpen,
  position,
  items,
  onClose,
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);
  const [adjustedPosition, setAdjustedPosition] = useState(position);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
      window.addEventListener("resize", handleResize);
      window.addEventListener("scroll", handleResize);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleResize);
    };
  }, [isOpen, onClose]);

  const calculatePosition = () => {
    if (!menuRef.current || !isOpen) return;

    const menuRect = menuRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Menu dimensions (estimate if not rendered yet)
    const menuWidth = menuRect.width || 200; // fallback width
    const menuHeight = menuRect.height || items.length * 40 + 16; // estimate height

    let newX = position.x;
    let newY = position.y;

    // Horizontal positioning
    if (position.x + menuWidth > viewportWidth) {
      // Try to position to the left of cursor
      newX = position.x - menuWidth;
      // If still outside viewport, clamp to right edge
      if (newX < 0) {
        newX = viewportWidth - menuWidth - 10;
      }
    }

    // Vertical positioning
    if (position.y + menuHeight > viewportHeight) {
      // Try to position above cursor
      newY = position.y - menuHeight;
      // If still outside viewport, clamp to bottom edge
      if (newY < 0) {
        newY = viewportHeight - menuHeight - 10;
      }
    }

    // Ensure minimum margins from viewport edges
    newX = Math.max(10, Math.min(newX, viewportWidth - menuWidth - 10));
    newY = Math.max(10, Math.min(newY, viewportHeight - menuHeight - 10));

    setAdjustedPosition({ x: newX, y: newY });
  };

  useEffect(() => {
    if (isOpen) {
      // Small delay to ensure menu is rendered before calculating position
      const timer = setTimeout(calculatePosition, 0);
      return () => clearTimeout(timer);
    }
  }, [isOpen, position, items.length]);

  if (!isOpen) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-48"
      style={{
        left: adjustedPosition.x,
        top: adjustedPosition.y,
      }}
    >
      {items.map((item, index) => (
        <React.Fragment key={item.id}>
          {item.divider && index > 0 && (
            <div className="border-t border-gray-100 my-1" />
          )}
          <button
            onClick={() => {
              if (!item.disabled) {
                item.onClick();
                onClose();
              }
            }}
            disabled={item.disabled}
            className={`w-full flex items-center px-3 py-2 text-sm text-left transition-colors ${
              item.disabled
                ? "text-gray-400 cursor-not-allowed"
                : item.destructive
                ? "text-red-600 hover:bg-red-50"
                : "text-gray-700 hover:bg-gray-50"
            }`}
          >
            <item.icon className="h-4 w-4 mr-3 flex-shrink-0" />
            {item.label}
          </button>
        </React.Fragment>
      ))}
    </div>
  );
}

// Hook for managing context menu state
export function useContextMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });

  const openContextMenu = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setPosition({ x: event.clientX, y: event.clientY });
    setIsOpen(true);
  };

  const closeContextMenu = () => {
    setIsOpen(false);
  };

  return {
    isOpen,
    position,
    openContextMenu,
    closeContextMenu,
  };
}

// Predefined context menu items for common actions
export const createFolderContextMenuItems = (
  _folder: any,
  actions: {
    onOpen?: () => void;
    onRename?: () => void;
    onDelete?: () => void;
    onMove?: () => void;
    onCopy?: () => void;
    onCreateSubfolder?: () => void;
    onShare?: () => void;
  }
): ContextMenuItem[] => [
  {
    id: "open",
    label: "Open",
    icon: EyeIcon,
    onClick: actions.onOpen || (() => {}),
    disabled: !actions.onOpen,
  },
  {
    id: "create-subfolder",
    label: "Create Subfolder",
    icon: FolderPlusIcon,
    onClick: actions.onCreateSubfolder || (() => {}),
    disabled: !actions.onCreateSubfolder,
    divider: true,
  },
  {
    id: "rename",
    label: "Rename",
    icon: PencilIcon,
    onClick: actions.onRename || (() => {}),
    disabled: !actions.onRename,
  },
  {
    id: "copy",
    label: "Copy",
    icon: DocumentDuplicateIcon,
    onClick: actions.onCopy || (() => {}),
    disabled: !actions.onCopy,
  },
  {
    id: "move",
    label: "Move",
    icon: ArrowRightIcon,
    onClick: actions.onMove || (() => {}),
    disabled: !actions.onMove,
  },
  {
    id: "share",
    label: "Share",
    icon: ShareIcon,
    onClick: actions.onShare || (() => {}),
    disabled: !actions.onShare,
    divider: true,
  },
  {
    id: "delete",
    label: "Delete",
    icon: TrashIcon,
    onClick: actions.onDelete || (() => {}),
    disabled: !actions.onDelete,
    destructive: true,
  },
];

export const createDocumentContextMenuItems = (
  _document: any,
  actions: {
    onPreview?: () => void;
    onDownload?: () => void;
    onRename?: () => void;
    onDelete?: () => void;
    onMove?: () => void;
    onCopy?: () => void;
    onShare?: () => void;
  }
): ContextMenuItem[] => [
  {
    id: "preview",
    label: "Preview",
    icon: EyeIcon,
    onClick: actions.onPreview || (() => {}),
    disabled: !actions.onPreview,
  },
  {
    id: "download",
    label: "Download",
    icon: ArrowDownTrayIcon,
    onClick: actions.onDownload || (() => {}),
    disabled: !actions.onDownload,
    divider: true,
  },
  {
    id: "rename",
    label: "Rename",
    icon: PencilIcon,
    onClick: actions.onRename || (() => {}),
    disabled: !actions.onRename,
  },
  {
    id: "copy",
    label: "Copy",
    icon: DocumentDuplicateIcon,
    onClick: actions.onCopy || (() => {}),
    disabled: !actions.onCopy,
  },
  {
    id: "move",
    label: "Move",
    icon: ArrowRightIcon,
    onClick: actions.onMove || (() => {}),
    disabled: !actions.onMove,
  },
  {
    id: "share",
    label: "Share",
    icon: ShareIcon,
    onClick: actions.onShare || (() => {}),
    disabled: !actions.onShare,
    divider: true,
  },
  {
    id: "delete",
    label: "Delete",
    icon: TrashIcon,
    onClick: actions.onDelete || (() => {}),
    disabled: !actions.onDelete,
    destructive: true,
  },
];
