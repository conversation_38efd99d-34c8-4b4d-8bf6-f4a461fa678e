import {
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import type { FileFilters } from "./FileFilters";
import type { DocumentFile } from "../../lib/fileService";

interface MobileFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FileFilters;
  onFiltersChange: (filters: FileFilters) => void;
  documents: DocumentFile[];
  viewMode: "grid" | "list";
  onViewModeChange: (mode: "grid" | "list") => void;
}

export function MobileFilterModal({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  documents,
  viewMode,
  onViewModeChange,
}: MobileFilterModalProps) {
  const updateFilter = (key: keyof FileFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const fileTypes = Array.from(
    new Set(documents.map((doc) => doc.file_type))
  ).sort();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-x-0 bottom-0 bg-white rounded-t-xl shadow-xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
          <div className="space-y-6">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Documents
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={filters.search}
                  onChange={(e) => updateFilter("search", e.target.value)}
                  className="input-field pl-10"
                />
              </div>
            </div>

            {/* View Mode */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                View Mode
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={() => onViewModeChange("grid")}
                  className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                    viewMode === "grid"
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => onViewModeChange("list")}
                  className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                    viewMode === "list"
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  List
                </button>
              </div>
            </div>

            {/* File Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                File Type
              </label>
              <select
                value={filters.fileType}
                onChange={(e) => updateFilter("fileType", e.target.value)}
                className="input-field"
              >
                <option value="">All Types</option>
                {fileTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => updateFilter("sortBy", e.target.value)}
                className="input-field"
              >
                <option value="name">Name</option>
                <option value="date">Date</option>
                <option value="size">Size</option>
                <option value="downloads">Downloads</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort Order
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={() => updateFilter("sortOrder", "asc")}
                  className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                    filters.sortOrder === "asc"
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  Ascending
                </button>
                <button
                  onClick={() => updateFilter("sortOrder", "desc")}
                  className={`flex-1 py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                    filters.sortOrder === "desc"
                      ? "bg-blue-100 text-blue-700 border border-blue-200"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  Descending
                </button>
              </div>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date Range
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => updateFilter("dateRange", e.target.value)}
                className="input-field"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
              </select>
            </div>

            {/* Size Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                File Size
              </label>
              <select
                value={filters.sizeRange}
                onChange={(e) => updateFilter("sizeRange", e.target.value)}
                className="input-field"
              >
                <option value="all">All Sizes</option>
                <option value="small">Small (&lt; 1MB)</option>
                <option value="medium">Medium (1-10MB)</option>
                <option value="large">Large (&gt; 10MB)</option>
              </select>
            </div>

            {/* Sharing Status */}
            {filters.sharingStatus !== null && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sharing Status
                </label>
                <select
                  value={filters.sharingStatus || "all"}
                  onChange={(e) =>
                    updateFilter(
                      "sharingStatus",
                      e.target.value === "all" ? null : e.target.value
                    )
                  }
                  className="input-field"
                >
                  <option value="all">All Documents</option>
                  <option value="owned">My Documents</option>
                  <option value="shared">Shared with Me</option>
                </select>
              </div>
            )}
          </div>

          {/* Apply Button */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button onClick={onClose} className="w-full btn-primary">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
