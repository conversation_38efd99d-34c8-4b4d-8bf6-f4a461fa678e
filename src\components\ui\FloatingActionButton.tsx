import React, { useState } from "react";
import {
  PlusIcon,
  FolderPlusIcon,
  DocumentPlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface FABAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  color?: string;
}

interface FloatingActionButtonProps {
  actions: FABAction[];
  className?: string;
}

export function FloatingActionButton({
  actions,
  className = "",
}: FloatingActionButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`fixed bottom-6 right-6 z-40 lg:hidden ${className}`}>
      {/* Action buttons */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 space-y-3 animate-fade-in">
          {actions.map((action, index) => (
            <div
              key={action.id}
              className="flex items-center space-x-3 animate-fab-slide-up"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <span className="bg-gray-800 text-white text-sm px-3 py-1 rounded-lg shadow-lg whitespace-nowrap">
                {action.label}
              </span>
              <button
                onClick={() => {
                  action.onClick();
                  setIsOpen(false);
                }}
                className={`
                  w-12 h-12 rounded-full shadow-lg transition-all duration-200 
                  flex items-center justify-center
                  ${action.color || "bg-blue-600 hover:bg-blue-700"}
                  active:scale-95
                `}
              >
                <action.icon className="h-6 w-6 text-white" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Main FAB button */}
      <button
        onClick={toggleOpen}
        className={`
          w-14 h-14 bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg 
          flex items-center justify-center transition-all duration-200
          active:scale-95 min-h-[44px] min-w-[44px]
          ${isOpen ? "rotate-45" : "rotate-0"}
        `}
        aria-label={isOpen ? "Close menu" : "Open menu"}
      >
        {isOpen ? (
          <XMarkIcon className="h-7 w-7 text-white" />
        ) : (
          <PlusIcon className="h-7 w-7 text-white" />
        )}
      </button>
    </div>
  );
}

// Predefined FAB configurations
export const createDocumentsFAB = (actions: {
  onCreateFolder?: () => void;
  onUploadDocument?: () => void;
}): FABAction[] => [
  ...(actions.onCreateFolder
    ? [
        {
          id: "create-folder",
          label: "Create Folder",
          icon: FolderPlusIcon,
          onClick: actions.onCreateFolder,
          color: "bg-green-600 hover:bg-green-700",
        },
      ]
    : []),
  ...(actions.onUploadDocument
    ? [
        {
          id: "upload-document",
          label: "Upload Document",
          icon: DocumentPlusIcon,
          onClick: actions.onUploadDocument,
          color: "bg-purple-600 hover:bg-purple-700",
        },
      ]
    : []),
];

/* Add animation styles to your CSS
const fabStyles = `
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}
`;
*/
