import React, { useState, useEffect } from "react";
import {
  XMarkIcon,
  ChevronRightIcon,
  HomeIcon,
} from "@heroicons/react/24/outline";
import { useAuth } from "../../contexts/AuthContext";
import { folderService, type Folder } from "../../lib/folderService";
import { fileService, type DocumentFile } from "../../lib/fileService";
import toast from "react-hot-toast";

interface FolderOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  operation: "copy" | "move" | "create_subfolder" | null;
  sourceFolder?: Folder | null;
  sourceDocument?: DocumentFile;
  onSuccess?: () => void;
}

export function FolderOperationsModal({
  isOpen,
  onClose,
  operation,
  sourceFolder,
  sourceDocument,
  onSuccess,
}: FolderOperationsModalProps) {
  const { user } = useAuth();
  const [allFolders, setAllFolders] = useState<Folder[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [newFolderName, setNewFolderName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [loadingFolders, setLoadingFolders] = useState(false);
  const [breadcrumbs, setBreadcrumbs] = useState<
    Array<{ id: string | null; name: string }>
  >([]);

  useEffect(() => {
    if (isOpen && user) {
      loadFolders();
    }
  }, [isOpen, user]);

  const loadFolders = async () => {
    if (!user) return;

    setLoadingFolders(true);
    try {
      const folders = await folderService.getAllFolders(user.id);
      // Filter out the source folder and its descendants for move/copy operations
      let availableFolders = folders;

      if (sourceFolder && (operation === "copy" || operation === "move")) {
        availableFolders = folders.filter((folder) => {
          // Exclude the source folder itself
          if (folder.id === sourceFolder.id) return false;
          // Exclude descendants of the source folder
          return !folder.path.includes(`/${sourceFolder.id}/`);
        });
      }

      setAllFolders(availableFolders);
      // Initialize breadcrumbs with root
      setBreadcrumbs([{ id: null, name: "My Documents" }]);
    } catch (error) {
      console.error("Failed to load folders:", error);
      toast.error("Failed to load folders");
    } finally {
      setLoadingFolders(false);
    }
  };

  // Get folders in the current directory
  const getCurrentFolders = () => {
    return allFolders.filter(
      (folder) => folder.parent_folder_id === currentFolderId
    );
  };

  // Navigate to a folder
  const navigateToFolder = (folderId: string | null) => {
    setCurrentFolderId(folderId);

    if (folderId === null) {
      // Navigating to root
      setBreadcrumbs([{ id: null, name: "My Documents" }]);
    } else {
      // Build breadcrumb path
      const folder = allFolders.find((f) => f.id === folderId);
      if (folder) {
        const path = buildBreadcrumbPath(folder);
        setBreadcrumbs(path);
      }
    }
  };

  // Build breadcrumb path for a folder
  const buildBreadcrumbPath = (
    folder: Folder
  ): Array<{ id: string | null; name: string }> => {
    const path: Array<{ id: string | null; name: string }> = [
      { id: null, name: "My Documents" },
    ];

    // Build path from root to current folder
    const pathIds = folder.path.split("/").filter((id) => id !== "");

    for (const pathId of pathIds) {
      const pathFolder = allFolders.find((f) => f.id === pathId);
      if (pathFolder) {
        path.push({ id: pathFolder.id, name: pathFolder.name });
      }
    }

    return path;
  };

  const handleSubmit = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      switch (operation) {
        case "copy":
          if (sourceFolder) {
            await folderService.copyFolder(
              sourceFolder.id,
              selectedFolderId,
              user.id
            );
            toast.success("Folder copied successfully");
          } else if (sourceDocument) {
            await fileService.copyDocument(
              sourceDocument.id,
              selectedFolderId,
              user.id
            );
            toast.success("Document copied successfully");
          }
          break;

        case "move":
          if (sourceFolder) {
            await folderService.moveFolder(
              sourceFolder.id,
              selectedFolderId,
              user.id
            );
            toast.success("Folder moved successfully");
          } else if (sourceDocument) {
            await fileService.moveDocumentsToFolder(
              [sourceDocument.id],
              selectedFolderId,
              user.id
            );
            toast.success("Document moved successfully");
          }
          break;

        case "create_subfolder":
          if (!newFolderName.trim()) {
            toast.error("Please enter a folder name");
            return;
          }
          if (sourceFolder) {
            await folderService.createSubfolder(
              sourceFolder.id,
              {
                name: newFolderName.trim(),
                description: "",
              },
              user.id
            );
            toast.success("Subfolder created successfully");
          }
          break;
      }

      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error("Operation failed:", error);
      toast.error(error.message || "Operation failed");
    } finally {
      setIsLoading(false);
    }
  };

  const getTitle = () => {
    switch (operation) {
      case "copy":
        return `Copy ${sourceFolder ? "Folder" : "Document"}`;
      case "move":
        return `Move ${sourceFolder ? "Folder" : "Document"}`;
      case "create_subfolder":
        return "Create Subfolder";
      default:
        return "Folder Operation";
    }
  };

  const getDescription = () => {
    const itemName = sourceFolder?.name || sourceDocument?.title || "";
    switch (operation) {
      case "copy":
        return `Select a destination folder to copy "${itemName}" to:`;
      case "move":
        return `Select a destination folder to move "${itemName}" to:`;
      case "create_subfolder":
        return `Create a new subfolder inside "${sourceFolder?.name}":`;
      default:
        return "";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">{getTitle()}</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          <p className="text-sm text-gray-600 mb-4">{getDescription()}</p>

          {operation === "create_subfolder" ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Folder Name
                </label>
                <input
                  type="text"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="Enter folder name"
                  className="input-field"
                  autoFocus
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Breadcrumb Navigation */}
              <div className="flex items-center space-x-1 text-sm text-gray-600 bg-gray-50 p-2 rounded-lg">
                {breadcrumbs.map((crumb, index) => (
                  <React.Fragment key={crumb.id || "root"}>
                    {index > 0 && (
                      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                    <button
                      onClick={() => navigateToFolder(crumb.id)}
                      className={`flex items-center space-x-1 px-2 py-1 rounded hover:bg-gray-200 transition-colors ${
                        index === breadcrumbs.length - 1
                          ? "font-medium text-gray-900"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {index === 0 ? (
                        <HomeIcon className="h-4 w-4" />
                      ) : (
                        <div className="text-sm">📁</div>
                      )}
                      <span className="truncate max-w-[120px]">
                        {crumb.name}
                      </span>
                    </button>
                  </React.Fragment>
                ))}
              </div>

              {/* Current Location Selection */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="text-xl">📁</div>
                    <span className="text-sm font-medium text-blue-900">
                      Select this location:{" "}
                      {breadcrumbs[breadcrumbs.length - 1]?.name}
                    </span>
                  </div>
                  <button
                    onClick={() => setSelectedFolderId(currentFolderId)}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      selectedFolderId === currentFolderId
                        ? "bg-blue-600 text-white"
                        : "bg-white text-blue-600 border border-blue-600 hover:bg-blue-600 hover:text-white"
                    }`}
                  >
                    {selectedFolderId === currentFolderId
                      ? "Selected"
                      : "Select"}
                  </button>
                </div>
              </div>

              {/* Folder Navigation */}
              <div className="max-h-48 overflow-y-auto space-y-1">
                {loadingFolders ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">
                      Loading folders...
                    </p>
                  </div>
                ) : (
                  <>
                    {getCurrentFolders().map((folder) => (
                      <div
                        key={folder.id}
                        className="flex items-center justify-between p-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                      >
                        <button
                          onClick={() => navigateToFolder(folder.id)}
                          className="flex items-center space-x-2 flex-1 text-left"
                        >
                          <div className="text-lg">📁</div>
                          <span className="text-sm font-medium truncate">
                            {folder.name}
                          </span>
                          <ChevronRightIcon className="h-4 w-4 text-gray-400 ml-auto" />
                        </button>
                      </div>
                    ))}

                    {getCurrentFolders().length === 0 && (
                      <div className="text-center py-4">
                        <div className="text-4xl mb-2">📁</div>
                        <p className="text-sm text-gray-500">
                          No subfolders in this location
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={onClose}
              className="btn-secondary"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              className="btn-primary"
              disabled={
                isLoading ||
                (operation === "create_subfolder" && !newFolderName.trim()) ||
                (operation !== "create_subfolder" &&
                  selectedFolderId === undefined)
              }
            >
              {isLoading
                ? "Processing..."
                : operation === "create_subfolder"
                ? "Create"
                : operation === "copy"
                ? "Copy"
                : "Move"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
