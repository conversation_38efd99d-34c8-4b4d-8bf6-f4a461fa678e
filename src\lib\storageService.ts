import { supabase } from "./supabase";

export interface StorageStats {
  totalDocuments: number;
  storageUsed: number;
  storageLimit: number;
  storagePercentage: number;
}

export const storageService = {
  // Storage limit in bytes (200MB)
  STORAGE_LIMIT: 200 * 1024 * 1024,

  // Get real-time storage statistics for a user
  async getStorageStats(userId: string): Promise<StorageStats> {
    try {
      const { data: documents, error } = await supabase
        .from("documents")
        .select("file_size")
        .eq("user_id", userId);

      if (error) throw error;

      const totalDocuments = documents?.length || 0;
      const storageUsed =
        documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;
      const storagePercentage = (storageUsed / this.STORAGE_LIMIT) * 100;

      return {
        totalDocuments,
        storageUsed,
        storageLimit: this.STORAGE_LIMIT,
        storagePercentage,
      };
    } catch (error: any) {
      console.error("Get storage stats error:", error);
      throw new Error(error.message || "Failed to get storage statistics");
    }
  },

  // Check if user has enough storage for a new file
  async checkStorageAvailable(
    userId: string,
    fileSize: number
  ): Promise<boolean> {
    try {
      const stats = await this.getStorageStats(userId);
      return stats.storageUsed + fileSize <= this.STORAGE_LIMIT;
    } catch (error) {
      console.error("Check storage available error:", error);
      return false;
    }
  },

  // Get storage usage by folder
  async getFolderStorageUsage(
    userId: string,
    folderId: string | null
  ): Promise<{
    documentCount: number;
    storageUsed: number;
  }> {
    try {
      let query = supabase
        .from("documents")
        .select("file_size")
        .eq("user_id", userId);

      if (folderId === null) {
        query = query.is("folder_id", null);
      } else {
        query = query.eq("folder_id", folderId);
      }

      const { data: documents, error } = await query;

      if (error) throw error;

      const documentCount = documents?.length || 0;
      const storageUsed =
        documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;

      return {
        documentCount,
        storageUsed,
      };
    } catch (error: any) {
      console.error("Get folder storage usage error:", error);
      throw new Error(error.message || "Failed to get folder storage usage");
    }
  },

  // Track storage change (for copy operations)
  async trackStorageChange(
    userId: string,
    operation: "add" | "remove",
    fileSize: number,
    _reason: string
  ): Promise<void> {
    try {
      // You could implement a storage_logs table here for auditing
      // For now, we'll just ensure the operation is valid
      if (operation === "add") {
        const hasSpace = await this.checkStorageAvailable(userId, fileSize);
        if (!hasSpace) {
          throw new Error("Insufficient storage space");
        }
      }
    } catch (error: any) {
      console.error("Track storage change error:", error);
      throw new Error(error.message || "Failed to track storage change");
    }
  },

  // Get storage breakdown by file type
  async getStorageBreakdown(userId: string): Promise<
    Array<{
      fileType: string;
      count: number;
      totalSize: number;
      percentage: number;
    }>
  > {
    try {
      const { data: documents, error } = await supabase
        .from("documents")
        .select("file_type, file_size")
        .eq("user_id", userId);

      if (error) throw error;

      const totalSize =
        documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;

      // Group by file type
      const breakdown =
        documents?.reduce((acc, doc) => {
          const type = doc.file_type || "Unknown";
          if (!acc[type]) {
            acc[type] = { count: 0, totalSize: 0 };
          }
          acc[type].count++;
          acc[type].totalSize += doc.file_size || 0;
          return acc;
        }, {} as Record<string, { count: number; totalSize: number }>) || {};

      // Convert to array with percentages
      return Object.entries(breakdown)
        .map(([fileType, data]) => ({
          fileType,
          count: data.count,
          totalSize: data.totalSize,
          percentage: totalSize > 0 ? (data.totalSize / totalSize) * 100 : 0,
        }))
        .sort((a, b) => b.totalSize - a.totalSize);
    } catch (error: any) {
      console.error("Get storage breakdown error:", error);
      throw new Error(error.message || "Failed to get storage breakdown");
    }
  },

  // Format file size for display
  formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 B";

    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  },

  // Get storage warning level
  getStorageWarningLevel(
    storagePercentage: number
  ): "safe" | "warning" | "critical" | "full" {
    if (storagePercentage >= 100) return "full";
    if (storagePercentage >= 90) return "critical";
    if (storagePercentage >= 75) return "warning";
    return "safe";
  },

  // Get storage warning message
  getStorageWarningMessage(stats: StorageStats): string | null {
    const level = this.getStorageWarningLevel(stats.storagePercentage);

    switch (level) {
      case "full":
        return "Storage is full. Please delete some files to upload new ones.";
      case "critical":
        return `Storage is ${stats.storagePercentage.toFixed(
          1
        )}% full. Consider cleaning up files.`;
      case "warning":
        return `Storage is ${stats.storagePercentage.toFixed(1)}% full.`;
      default:
        return null;
    }
  },

  // Clean up orphaned files (files in storage but not in database)
  async cleanupOrphanedFiles(userId: string): Promise<{
    cleaned: number;
    errors: string[];
  }> {
    try {
      // Get all files in user's storage folder
      const { data: storageFiles, error: storageError } = await supabase.storage
        .from("documents")
        .list(userId);

      if (storageError) throw storageError;

      // Get all file paths from database
      const { data: dbDocuments, error: dbError } = await supabase
        .from("documents")
        .select("file_path")
        .eq("user_id", userId);

      if (dbError) throw dbError;

      const dbFilePaths = new Set(
        dbDocuments?.map((doc) => doc.file_path.split("/").pop()) || []
      );

      // Find orphaned files
      const orphanedFiles =
        storageFiles?.filter((file) => !dbFilePaths.has(file.name)) || [];

      const errors: string[] = [];
      let cleaned = 0;

      // Remove orphaned files
      for (const file of orphanedFiles) {
        try {
          const { error } = await supabase.storage
            .from("documents")
            .remove([`${userId}/${file.name}`]);

          if (error) {
            errors.push(`Failed to delete ${file.name}: ${error.message}`);
          } else {
            cleaned++;
          }
        } catch (error: any) {
          errors.push(`Failed to delete ${file.name}: ${error.message}`);
        }
      }

      return { cleaned, errors };
    } catch (error: any) {
      console.error("Cleanup orphaned files error:", error);
      throw new Error(error.message || "Failed to cleanup orphaned files");
    }
  },
};
