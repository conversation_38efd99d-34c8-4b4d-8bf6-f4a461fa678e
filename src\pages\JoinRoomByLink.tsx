import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { invitationService } from "../lib/invitationService";
import { notificationService } from "../lib/notificationService";
import type { InvitationLinkWithDetails } from "../lib/invitationService";
import { usePageTitle } from "../hooks/usePageTitle";
import toast from "react-hot-toast";

export function JoinRoomByLink() {
  const { token } = useParams<{ token: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [invitationLink, setInvitationLink] = useState<InvitationLinkWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Set page title
  usePageTitle("Join Room");

  useEffect(() => {
    if (token) {
      loadInvitationLink();
    }
  }, [token]);

  const loadInvitationLink = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      const link = await invitationService.getInvitationByToken(token);
      
      if (!link) {
        setError("This invitation link is invalid, expired, or has reached its usage limit.");
      } else {
        setInvitationLink(link);
      }
    } catch (error: any) {
      console.error("Failed to load invitation link:", error);
      setError("Failed to load invitation details. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleJoinRoom = async () => {
    if (!user || !token || !invitationLink) return;

    try {
      setIsJoining(true);

      await invitationService.joinRoomViaLink(token, user.id);

      // Notify room admin about new member
      await notificationService.notifyRoomJoin({
        roomAdminId: invitationLink.created_by,
        memberName: user.profile?.full_name || user.email || "Someone",
        roomName: invitationLink.room.name,
        roomId: invitationLink.room.id,
        memberId: user.id,
      });

      toast.success(`Welcome to "${invitationLink.room.name}"!`);
      navigate(`/rooms/${invitationLink.room.id}`);
    } catch (error: any) {
      toast.error(error.message || "Failed to join room");
    } finally {
      setIsJoining(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <span className="text-4xl">🎒</span>
            <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
              Join SmartBagPack Room
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              You need to sign in or create an account to join this room
            </p>
          </div>

          <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
              <div className="space-y-4">
                <button
                  onClick={() => navigate('/login')}
                  className="btn-primary w-full"
                >
                  Sign In
                </button>
                <button
                  onClick={() => navigate('/register')}
                  className="btn-secondary w-full"
                >
                  Create Account
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-sm text-gray-600">Loading invitation...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <span className="text-4xl">❌</span>
            <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
              Invalid Invitation
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600">
              {error}
            </p>
          </div>

          <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
            <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
              <button
                onClick={() => navigate('/rooms')}
                className="btn-primary w-full"
              >
                Browse Rooms
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <span className="text-4xl">🎒</span>
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Join Room
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            You've been invited to join a room on SmartBagPack
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            {invitationLink && (
              <div className="space-y-6">
                {/* Room Info */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {invitationLink.room.name}
                  </h3>
                  <div className="flex items-center justify-center space-x-2 mt-2">
                    <span className="text-sm text-gray-500">
                      Room Code: {invitationLink.room.room_code}
                    </span>
                    {invitationLink.room.is_private && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2-2v6a2 2 0 002 2z" />
                        </svg>
                        Private
                      </span>
                    )}
                  </div>
                </div>

                {/* Invitation Details */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Invited by:</span>{" "}
                    {invitationLink.created_by_profile.full_name || invitationLink.created_by_profile.email}
                  </p>
                  <div className="mt-2 text-xs text-gray-500">
                    <p>Uses: {invitationLink.current_uses}{invitationLink.max_uses ? ` / ${invitationLink.max_uses}` : ' (unlimited)'}</p>
                    <p>Expires: {new Date(invitationLink.expires_at).toLocaleDateString()}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handleJoinRoom}
                    disabled={isJoining}
                    className="btn-primary w-full disabled:opacity-50"
                  >
                    {isJoining ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Joining Room...
                      </div>
                    ) : (
                      "Join Room"
                    )}
                  </button>
                  
                  <button
                    onClick={() => navigate('/rooms')}
                    disabled={isJoining}
                    className="btn-secondary w-full"
                  >
                    Browse Other Rooms
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
