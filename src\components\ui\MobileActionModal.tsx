import React from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import type { ContextMenuItem } from "./ContextMenu";

interface MobileActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  items: ContextMenuItem[];
  isLoading?: boolean;
}

export function MobileActionModal({
  isOpen,
  onClose,
  title,
  items,
  isLoading = false,
}: MobileActionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onClose();
        }}
      />

      {/* Modal */}
      <div className="fixed inset-x-0 bottom-0 bg-white rounded-t-xl shadow-xl max-h-[70vh] overflow-hidden animate-slide-up">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 truncate">
            {title}
          </h3>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <XMarkIcon className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Actions */}
        <div className="p-2 overflow-y-auto max-h-[calc(70vh-80px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading...</span>
            </div>
          ) : (
            items.map((item, index) => (
              <React.Fragment key={item.id}>
                {item.divider && index > 0 && (
                  <div className="border-t border-gray-100 my-2" />
                )}
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (!item.disabled) {
                      item.onClick();
                      onClose();
                    }
                  }}
                  disabled={item.disabled}
                  className={`w-full flex items-center px-4 py-4 text-left transition-colors rounded-lg ${
                    item.disabled
                      ? "text-gray-400 cursor-not-allowed"
                      : item.destructive
                      ? "text-red-600 hover:bg-red-50 active:bg-red-100"
                      : "text-gray-700 hover:bg-gray-50 active:bg-gray-100"
                  } min-h-[44px]`} // Ensure 44px minimum touch target
                >
                  <item.icon className="h-6 w-6 mr-4 flex-shrink-0" />
                  <span className="font-medium">{item.label}</span>
                </button>
              </React.Fragment>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

// Hook for managing mobile action modal state
export function useMobileActionModal() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [title, setTitle] = React.useState("");
  const [items, setItems] = React.useState<ContextMenuItem[]>([]);

  const openModal = (modalTitle: string, modalItems: ContextMenuItem[]) => {
    setTitle(modalTitle);
    setItems(modalItems);
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  return {
    isOpen,
    title,
    items,
    openModal,
    closeModal,
  };
}

/* Add slide-up animation styles to your CSS
const slideUpStyles = `
@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}
`;
*/
