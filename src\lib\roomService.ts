import { supabase } from "./supabase";
import type { Room, DocumentFile } from "./fileService";
import { notificationService } from "./notificationService";

export interface RoomMember {
  id: string;
  room_id: string;
  user_id: string;
  role: "admin" | "member";
  joined_at: string;
  profiles?: {
    full_name: string | null;
    email: string;
    role: string;
  };
}

export interface RoomDocument {
  id: string;
  room_id: string;
  document_id: string;
  shared_by: string;
  permission: "view" | "download";
  shared_at: string;
  documents?: DocumentFile;
  shared_by_profile?: {
    full_name: string | null;
    email: string;
  };
}

export interface RoomWithDetails extends Room {
  member_count?: number;
  document_count?: number;
  is_member?: boolean;
  user_role?: "admin" | "member" | null;
}

export const roomService = {
  // Generate unique room code
  generateRoomCode(): string {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Create a new room
  async createRoom(data: {
    name: string;
    description?: string;
    is_private?: boolean;
    max_members?: number;
    created_by: string;
  }): Promise<Room> {
    try {
      let roomCode = this.generateRoomCode();

      // Ensure room code is unique
      let isUnique = false;
      while (!isUnique) {
        const { data: existing } = await supabase
          .from("rooms")
          .select("id")
          .eq("room_code", roomCode.toUpperCase())
          .single();

        if (!existing) {
          isUnique = true;
        } else {
          roomCode = this.generateRoomCode();
        }
      }

      const { data: room, error } = await supabase
        .from("rooms")
        .insert({
          name: data.name,
          description: data.description || null,
          room_code: roomCode.toUpperCase(), // Ensure room code is always uppercase
          created_by: data.created_by,
          is_private: data.is_private || false,
          max_members: data.max_members || 100,
        })
        .select()
        .single();

      if (error) throw error;

      return room;
    } catch (error: any) {
      console.error("Create room error:", error);
      throw new Error(error.message || "Failed to create room");
    }
  },

  // Get user's rooms (rooms they created or are members of)
  async getUserRooms(userId: string): Promise<RoomWithDetails[]> {
    try {
      // First get rooms created by user
      const { data: createdRooms, error: createdError } = await supabase
        .from("rooms")
        .select("*")
        .eq("created_by", userId)
        .order("created_at", { ascending: false });

      if (createdError) throw createdError;

      // Then get rooms where user is a member
      const { data: memberRooms, error: memberError } = await supabase
        .from("room_members")
        .select(
          `
          room_id,
          role,
          rooms(*)
        `
        )
        .eq("user_id", userId);

      if (memberError) throw memberError;

      // Combine and deduplicate rooms
      const allRooms = [...(createdRooms || [])];

      // Add member rooms that aren't already in created rooms
      (memberRooms || []).forEach((membership: any) => {
        if (
          membership.rooms &&
          !allRooms.find((r) => r.id === membership.rooms.id)
        ) {
          allRooms.push({
            ...membership.rooms,
            user_role: membership.role,
          });
        }
      });

      // Get counts for each room
      const roomsWithDetails = await Promise.all(
        allRooms.map(async (room) => {
          // Get member count and ensure creator is included
          const { data: roomMembers } = await supabase
            .from("room_members")
            .select("user_id")
            .eq("room_id", room.id);

          // Check if creator is in members list
          const creatorInMembers = roomMembers?.find(
            (m) => m.user_id === room.created_by
          );
          let totalMembers = roomMembers?.length || 0;

          // If creator is not in members (shouldn't happen with trigger, but let's be safe)
          if (!creatorInMembers) {
            totalMembers += 1; // Add creator to count
          }

          // Get document count
          const { count: documentCount } = await supabase
            .from("room_documents")
            .select("*", { count: "exact", head: true })
            .eq("room_id", room.id);

          return {
            ...room,
            member_count: totalMembers,
            document_count: documentCount || 0,
            is_member: true,
            user_role:
              room.created_by === userId ? "admin" : room.user_role || "member",
          };
        })
      );

      return roomsWithDetails;
    } catch (error: any) {
      console.error("Get user rooms error:", error);
      // Return empty array to prevent app crash
      return [];
    }
  },

  // Search rooms by name or room code (including private rooms)
  async searchRooms(query: string, userId: string): Promise<RoomWithDetails[]> {
    try {
      // Search for ALL rooms by name or room code (both public and private)
      // Note: This should find both public and private rooms for join functionality
      const { data: rooms, error } = await supabase
        .from("rooms")
        .select("*")
        .or(`name.ilike.%${query}%,room_code.ilike.%${query}%`)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Search error:", error);
        throw error;
      }
      if (!rooms || rooms.length === 0) return [];

      // Get additional details for each room
      const roomsWithDetails = await Promise.all(
        rooms.map(async (room) => {
          // Check if user is a member
          const { data: membership } = await supabase
            .from("room_members")
            .select("role")
            .eq("room_id", room.id)
            .eq("user_id", userId)
            .single();

          // Get member count and ensure creator is included
          const { data: roomMembers } = await supabase
            .from("room_members")
            .select("user_id")
            .eq("room_id", room.id);

          // Check if creator is in members list
          const creatorInMembers = roomMembers?.find(
            (m) => m.user_id === room.created_by
          );
          let totalMembers = roomMembers?.length || 0;

          // If creator is not in members (shouldn't happen with trigger, but let's be safe)
          if (!creatorInMembers) {
            totalMembers += 1; // Add creator to count
          }

          // Get document count
          const { count: documentCount } = await supabase
            .from("room_documents")
            .select("*", { count: "exact", head: true })
            .eq("room_id", room.id);

          return {
            ...room,
            member_count: totalMembers,
            document_count: documentCount || 0,
            is_member: !!membership || room.created_by === userId,
            user_role:
              room.created_by === userId ? "admin" : membership?.role || null,
          };
        })
      );

      // Return all rooms (both public and private)
      return roomsWithDetails;
    } catch (error: any) {
      console.error("Search rooms error:", error);
      return [];
    }
  },

  // Join a room by room code
  async joinRoom(roomCode: string, userId: string): Promise<void> {
    try {
      // Find room by room code
      const { data: room, error: roomError } = await supabase
        .from("rooms")
        .select("id, name, max_members, created_by, is_private")
        .eq("room_code", roomCode.toUpperCase())
        .single();

      if (roomError || !room) {
        throw new Error(
          "Room not found. Please check the room code and try again."
        );
      }

      if (room.created_by === userId) {
        throw new Error("You are already the admin of this room");
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from("room_members")
        .select("id")
        .eq("room_id", room.id)
        .eq("user_id", userId)
        .single();

      if (existingMember) {
        throw new Error("You are already a member of this room");
      }

      // Get current member count (creator is already included via database trigger)
      const { count: memberCount } = await supabase
        .from("room_members")
        .select("*", { count: "exact", head: true })
        .eq("room_id", room.id);

      // Check if room is full (creator already counted in memberCount)
      if ((memberCount || 0) >= room.max_members) {
        throw new Error("Room is full");
      }

      // Add user to room
      const { error: joinError } = await supabase.from("room_members").insert({
        room_id: room.id,
        user_id: userId,
        role: "member",
      });

      if (joinError) throw joinError;

      // Get user profile for notification
      const { data: userProfile } = await supabase
        .from("profiles")
        .select("full_name, email")
        .eq("id", userId)
        .single();

      // Send notification to room admin
      try {
        await notificationService.notifyRoomJoin({
          roomAdminId: room.created_by,
          memberName: userProfile?.full_name || userProfile?.email || "Someone",
          roomName: room.name,
          roomId: room.id,
          memberId: userId,
        });
      } catch (notificationError) {
        console.error("Failed to send join notification:", notificationError);
        // Don't throw error here as the join was successful
      }
    } catch (error: any) {
      console.error("Join room error:", error);
      throw new Error(error.message || "Failed to join room");
    }
  },

  // Leave a room
  async leaveRoom(roomId: string, userId: string): Promise<void> {
    try {
      // Get room and user details for notification
      const { data: room } = await supabase
        .from("rooms")
        .select("created_by, name")
        .eq("id", roomId)
        .single();

      if (room?.created_by === userId) {
        throw new Error(
          "Room creators cannot leave their own rooms. Delete the room instead."
        );
      }

      const { data: userProfile } = await supabase
        .from("profiles")
        .select("full_name, email")
        .eq("id", userId)
        .single();

      const { error } = await supabase
        .from("room_members")
        .delete()
        .eq("room_id", roomId)
        .eq("user_id", userId);

      if (error) throw error;

      // Send notification to room admin
      if (room) {
        try {
          await notificationService.notifyRoomLeave({
            roomAdminId: room.created_by,
            memberName:
              userProfile?.full_name || userProfile?.email || "Someone",
            roomName: room.name,
            roomId: roomId,
            memberId: userId,
          });
        } catch (notificationError) {
          console.error(
            "Failed to send leave notification:",
            notificationError
          );
          // Don't throw error here as the leave was successful
        }
      }
    } catch (error: any) {
      console.error("Leave room error:", error);
      throw new Error(error.message || "Failed to leave room");
    }
  },

  // Get room members
  async getRoomMembers(roomId: string): Promise<RoomMember[]> {
    try {
      const { data, error } = await supabase
        .from("room_members")
        .select("*")
        .eq("room_id", roomId)
        .order("joined_at", { ascending: true });

      if (error) throw error;

      // Fetch profile information separately for each member
      const membersWithProfiles = await Promise.all(
        (data || []).map(async (member) => {
          const { data: profile } = await supabase
            .from("profiles")
            .select("full_name, email, role")
            .eq("id", member.user_id)
            .single();

          return {
            ...member,
            profiles: profile || null,
          };
        })
      );

      return membersWithProfiles;
    } catch (error: any) {
      console.error("Get room members error:", error);
      throw new Error(error.message || "Failed to fetch room members");
    }
  },

  // Share document to room
  async shareDocumentToRoom(
    documentId: string,
    roomId: string,
    sharedBy: string,
    permission: "view" | "download" = "download"
  ): Promise<void> {
    try {
      // Check if user is a member of the room
      const { data: membership } = await supabase
        .from("room_members")
        .select("id")
        .eq("room_id", roomId)
        .eq("user_id", sharedBy)
        .single();

      const { data: room } = await supabase
        .from("rooms")
        .select("created_by")
        .eq("id", roomId)
        .single();

      if (!membership && room?.created_by !== sharedBy) {
        throw new Error("You must be a member of the room to share documents");
      }

      // Check if document is already shared to this room
      const { data: existing } = await supabase
        .from("room_documents")
        .select("id")
        .eq("room_id", roomId)
        .eq("document_id", documentId)
        .single();

      if (existing) {
        throw new Error("Document is already shared to this room");
      }

      // Share the document
      const { error } = await supabase.from("room_documents").insert({
        room_id: roomId,
        document_id: documentId,
        shared_by: sharedBy,
        permission,
      });

      if (error) throw error;

      // Get room, document, and user details for notifications
      const { data: roomDetails } = await supabase
        .from("rooms")
        .select("name")
        .eq("id", roomId)
        .single();

      const { data: documentDetails } = await supabase
        .from("documents")
        .select("title")
        .eq("id", documentId)
        .single();

      const { data: sharerProfile } = await supabase
        .from("profiles")
        .select("full_name, email")
        .eq("id", sharedBy)
        .single();

      // Get all room members except the sharer
      const { data: roomMembers } = await supabase
        .from("room_members")
        .select("user_id")
        .eq("room_id", roomId)
        .neq("user_id", sharedBy);

      // Send notifications to room members
      if (
        roomDetails &&
        documentDetails &&
        roomMembers &&
        roomMembers.length > 0
      ) {
        try {
          const memberIds = roomMembers.map((member) => member.user_id);
          await notificationService.notifyDocumentSharedToRoom({
            memberIds,
            sharerName:
              sharerProfile?.full_name || sharerProfile?.email || "Someone",
            documentTitle: documentDetails.title,
            roomName: roomDetails.name,
            roomId: roomId,
            documentId: documentId,
          });
        } catch (notificationError) {
          console.error(
            "Failed to send room share notifications:",
            notificationError
          );
          // Don't throw error here as the share was successful
        }
      }
    } catch (error: any) {
      console.error("Share document to room error:", error);
      throw new Error(error.message || "Failed to share document to room");
    }
  },

  // Get room documents
  async getRoomDocuments(roomId: string): Promise<RoomDocument[]> {
    try {
      const { data, error } = await supabase
        .from("room_documents")
        .select(
          `
          *,
          documents(*)
        `
        )
        .eq("room_id", roomId)
        .order("shared_at", { ascending: false });

      if (error) throw error;

      // Fetch profile information separately for each document
      const documentsWithProfiles = await Promise.all(
        (data || []).map(async (roomDoc) => {
          const { data: profile } = await supabase
            .from("profiles")
            .select("full_name, email")
            .eq("id", roomDoc.shared_by)
            .single();

          return {
            ...roomDoc,
            shared_by_profile: profile || null,
          };
        })
      );

      return documentsWithProfiles;
    } catch (error: any) {
      console.error("Get room documents error:", error);
      throw new Error(error.message || "Failed to fetch room documents");
    }
  },

  // Get room details with members and stats
  async getRoomDetails(
    roomId: string,
    userId: string
  ): Promise<RoomWithDetails & { members: RoomMember[] }> {
    try {
      // Get room basic info
      const { data: room, error: roomError } = await supabase
        .from("rooms")
        .select("*")
        .eq("id", roomId)
        .single();

      if (roomError) throw roomError;
      if (!room) throw new Error("Room not found");

      // Check if user is a member or admin
      const { data: membership } = await supabase
        .from("room_members")
        .select("role")
        .eq("room_id", roomId)
        .eq("user_id", userId)
        .single();

      const isCreator = room.created_by === userId;
      const isMember = !!membership || isCreator;

      if (!isMember) {
        throw new Error("You don't have access to this room");
      }

      // Get members
      const { data: members, error: membersError } = await supabase
        .from("room_members")
        .select("*")
        .eq("room_id", roomId)
        .order("joined_at", { ascending: true });

      if (membersError) throw membersError;

      // Check if room creator is in the members list
      const creatorInMembers = members?.find(
        (m) => m.user_id === room.created_by
      );

      // If creator is not in members (shouldn't happen with trigger, but let's be safe)
      if (!creatorInMembers) {
        // Add creator manually
        const creatorMember = {
          id: `creator-${room.created_by}`,
          room_id: roomId,
          user_id: room.created_by,
          role: "admin" as const,
          joined_at: room.created_at,
        };
        members?.push(creatorMember);
      }

      // Fetch profile information separately for each member
      const membersWithProfiles = await Promise.all(
        (members || []).map(async (member) => {
          const { data: profile } = await supabase
            .from("profiles")
            .select("full_name, email, role")
            .eq("id", member.user_id)
            .single();

          return {
            ...member,
            profiles: profile || null,
          };
        })
      );

      // Get document count
      const { count: documentCount } = await supabase
        .from("room_documents")
        .select("*", { count: "exact", head: true })
        .eq("room_id", roomId);

      return {
        ...room,
        member_count: membersWithProfiles.length,
        document_count: documentCount || 0,
        is_member: true,
        user_role: isCreator ? "admin" : membership?.role || "member",
        members: membersWithProfiles,
      };
    } catch (error: any) {
      console.error("Get room details error:", error);
      throw new Error(error.message || "Failed to fetch room details");
    }
  },

  // Remove document from room
  async removeDocumentFromRoom(
    roomDocumentId: string,
    userId: string
  ): Promise<void> {
    try {
      // Check if user has permission to remove (document owner, room admin, or person who shared it)
      const { data: roomDocument, error: fetchError } = await supabase
        .from("room_documents")
        .select(
          `
          *,
          documents(user_id),
          rooms(created_by)
        `
        )
        .eq("id", roomDocumentId)
        .single();

      if (fetchError) throw fetchError;
      if (!roomDocument) throw new Error("Room document not found");

      const canRemove =
        roomDocument.shared_by === userId ||
        roomDocument.documents?.user_id === userId ||
        roomDocument.rooms?.created_by === userId;

      if (!canRemove) {
        throw new Error("Not authorized to remove this document from the room");
      }

      const { error } = await supabase
        .from("room_documents")
        .delete()
        .eq("id", roomDocumentId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Remove document from room error:", error);
      throw new Error(error.message || "Failed to remove document from room");
    }
  },

  // Update room settings
  async updateRoom(
    roomId: string,
    updates: {
      name?: string;
      description?: string;
      is_private?: boolean;
      max_members?: number;
    },
    userId: string
  ): Promise<void> {
    try {
      // Check if user is room admin
      const { data: room } = await supabase
        .from("rooms")
        .select("created_by")
        .eq("id", roomId)
        .single();

      if (!room || room.created_by !== userId) {
        throw new Error("Only room admins can update room settings");
      }

      const { error } = await supabase
        .from("rooms")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", roomId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Update room error:", error);
      throw new Error(error.message || "Failed to update room");
    }
  },

  // Delete room (only by creator)
  async deleteRoom(roomId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("rooms")
        .delete()
        .eq("id", roomId)
        .eq("created_by", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Delete room error:", error);
      throw new Error(error.message || "Failed to delete room");
    }
  },

  // Remove member from room
  async removeMemberFromRoom(
    roomId: string,
    memberId: string,
    adminUserId: string
  ): Promise<void> {
    try {
      // Check if user is room admin
      const { data: room } = await supabase
        .from("rooms")
        .select("created_by")
        .eq("id", roomId)
        .single();

      if (!room || room.created_by !== adminUserId) {
        throw new Error("Only room admins can remove members");
      }

      // Cannot remove the room creator
      if (memberId === room.created_by) {
        throw new Error("Cannot remove the room creator");
      }

      const { error } = await supabase
        .from("room_members")
        .delete()
        .eq("room_id", roomId)
        .eq("user_id", memberId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Remove member error:", error);
      throw new Error(error.message || "Failed to remove member");
    }
  },
};
