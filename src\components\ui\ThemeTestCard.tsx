import { useTheme } from '../../contexts/ThemeContext';

export function ThemeTestCard() {
  const { theme } = useTheme();

  return (
    <div className="card p-6 max-w-md">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 transition-theme duration-theme">
        Theme Test Card
      </h3>
      
      <div className="space-y-3">
        <p className="text-sm text-gray-600 dark:text-gray-400 transition-theme duration-theme">
          Current theme: <span className="font-medium">{theme}</span>
        </p>
        
        <div className="flex space-x-2">
          <button className="btn-primary">Primary Button</button>
          <button className="btn-secondary">Secondary Button</button>
        </div>
        
        <input 
          type="text" 
          placeholder="Test input field" 
          className="input-field"
        />
        
        <div className="bg-muted p-3 rounded-lg">
          <p className="text-muted text-sm">
            This is a muted background with muted text to test the theme utilities.
          </p>
        </div>
        
        <div className="border-muted border-t pt-3">
          <p className="text-xs text-gray-500 dark:text-gray-400 transition-theme duration-theme">
            All elements should smoothly transition between light and dark themes.
          </p>
        </div>
      </div>
    </div>
  );
}
