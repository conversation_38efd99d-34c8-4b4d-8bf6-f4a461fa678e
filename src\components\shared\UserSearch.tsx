import React, { useState, useEffect, useRef } from "react";
import { userService } from "../../lib/userService";
import type { UserSearchResult } from "../../lib/userService";
import { useAuth } from "../../contexts/AuthContext";

interface UserSearchProps {
  onUserSelect: (user: UserSearchResult) => void;
  selectedUsers?: UserSearchResult[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  excludeUserIds?: string[];
}

export function UserSearch({
  onUserSelect,
  selectedUsers = [],
  placeholder = "Search users by name or email...",
  className = "",
  disabled = false,
  excludeUserIds = [],
}: UserSearchProps) {
  const { user } = useAuth();
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<UserSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [hasSearched, setHasSearched] = useState(false);

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Search users with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Reset states when query changes
    setSearchError(null);
    setHasSearched(false);

    if (!query.trim()) {
      setResults([]);
      setIsOpen(false);
      return;
    }

    if (query.length < 2) {
      setResults([]);
      setIsOpen(false);
      return;
    }

    searchTimeoutRef.current = setTimeout(async () => {
      if (!user) return;

      setIsLoading(true);
      setHasSearched(true);

      try {
        const searchResults = await userService.searchUsers(query, user.id);

        // Filter out already selected users and excluded users
        const selectedUserIds = selectedUsers.map((u) => u.id);
        const allExcludedIds = [...selectedUserIds, ...excludeUserIds];
        const filteredResults = searchResults.filter(
          (result) => !allExcludedIds.includes(result.id)
        );

        setResults(filteredResults);
        setIsOpen(true); // Always open to show results or no results message
        setSelectedIndex(-1);
        setSearchError(null);
      } catch (error: any) {
        setResults([]);
        setIsOpen(true); // Open to show error message
        setSearchError(error.message || "Search failed. Please try again.");
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, user, selectedUsers, excludeUserIds]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
  };

  const handleUserSelect = (selectedUser: UserSearchResult) => {
    onUserSelect(selectedUser);
    setQuery("");
    setResults([]);
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleUserSelect(results[selectedIndex]);
        }
        break;
      case "Escape":
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const getUserInitials = (user: UserSearchResult): string => {
    return userService.getUserInitials(user);
  };

  const getUserDisplayName = (user: UserSearchResult): string => {
    return userService.getUserDisplayName(user);
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          className="input-field pr-10"
        />

        {/* Search icon */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {isLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          ) : (
            <svg
              className="h-4 w-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          )}
        </div>
      </div>

      {/* Search results dropdown */}
      {isOpen && !isLoading && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {results.map((result, index) => (
            <button
              key={result.id}
              onClick={() => handleUserSelect(result)}
              className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0 ${
                index === selectedIndex ? "bg-blue-50" : ""
              }`}
            >
              <div className="flex items-center space-x-3">
                {/* User avatar */}
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-medium text-sm">
                      {getUserInitials(result)}
                    </span>
                  </div>
                </div>

                {/* User info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {getUserDisplayName(result)}
                  </p>
                  <p className="text-sm text-gray-500 truncate">
                    {result.email}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                      {userService.formatUserRole(result.role)}
                    </span>
                    {result.institution && (
                      <span className="text-xs text-gray-500 truncate">
                        {result.institution}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Loading state */}
      {isOpen && isLoading && query.length >= 2 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="px-4 py-3 text-sm text-gray-500 text-center flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span>Searching for users...</span>
          </div>
        </div>
      )}

      {/* Error message */}
      {isOpen && !isLoading && searchError && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-red-200 rounded-lg shadow-lg">
          <div className="px-4 py-3 text-sm text-red-600 text-center">
            <div className="flex items-center justify-center space-x-2">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>{searchError}</span>
            </div>
          </div>
        </div>
      )}

      {/* No results message */}
      {isOpen &&
        !isLoading &&
        !searchError &&
        hasSearched &&
        query.length >= 2 &&
        results.length === 0 && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                <span>No users found matching "{query}"</span>
              </div>
              <div className="text-xs text-gray-400">
                Try searching by full name or email address
              </div>
            </div>
          </div>
        )}

      {/* Short query message */}
      {isOpen && !isLoading && query.length > 0 && query.length < 2 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="px-4 py-3 text-sm text-gray-500 text-center">
            Type at least 2 characters to search
          </div>
        </div>
      )}
    </div>
  );
}
