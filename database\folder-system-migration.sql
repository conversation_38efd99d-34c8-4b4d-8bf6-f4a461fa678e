-- SmartBagPack Folder System Migration
-- Run this SQL in your Supabase SQL Editor to add folder functionality

-- Create folders table
CREATE TABLE IF NOT EXISTS folders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
  path TEXT NOT NULL, -- Materialized path for efficient queries (e.g., '/folder1/subfolder2/')
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, parent_folder_id, name), -- Prevent duplicate folder names in same parent
  CHECK (parent_folder_id != id) -- Prevent self-reference
);

-- Add folder_id column to documents table (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'documents' AND column_name = 'folder_id') THEN
    ALTER TABLE documents ADD COLUMN folder_id UUID REFERENCES folders(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_folders_user_id ON folders(user_id);
CREATE INDEX IF NOT EXISTS idx_folders_parent_folder_id ON folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path);
CREATE INDEX IF NOT EXISTS idx_documents_folder_id ON documents(folder_id);

-- Enable Row Level Security for folders
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;

-- RLS Policies for folders
CREATE POLICY "Users can view own folders" ON folders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create folders" ON folders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own folders" ON folders FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own folders" ON folders FOR DELETE USING (auth.uid() = user_id);

-- Function to update folder path automatically
CREATE OR REPLACE FUNCTION update_folder_path()
RETURNS TRIGGER AS $$
DECLARE
  parent_path TEXT;
BEGIN
  -- If this is a root folder (no parent), set path to just the folder name
  IF NEW.parent_folder_id IS NULL THEN
    NEW.path := '/' || NEW.name || '/';
  ELSE
    -- Get parent folder path
    SELECT path INTO parent_path FROM folders WHERE id = NEW.parent_folder_id;
    IF parent_path IS NULL THEN
      RAISE EXCEPTION 'Parent folder not found';
    END IF;
    -- Construct new path
    NEW.path := parent_path || NEW.name || '/';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to update child folder paths when parent path changes
CREATE OR REPLACE FUNCTION update_child_folder_paths()
RETURNS TRIGGER AS $$
BEGIN
  -- Update all child folders' paths when parent path changes
  IF OLD.path != NEW.path THEN
    UPDATE folders 
    SET path = REPLACE(path, OLD.path, NEW.path)
    WHERE path LIKE OLD.path || '%' AND id != NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update folder paths on insert/update
CREATE TRIGGER trigger_update_folder_path
  BEFORE INSERT OR UPDATE ON folders
  FOR EACH ROW
  EXECUTE FUNCTION update_folder_path();

-- Create trigger to update child paths when parent path changes
CREATE TRIGGER trigger_update_child_folder_paths
  AFTER UPDATE ON folders
  FOR EACH ROW
  EXECUTE FUNCTION update_child_folder_paths();

-- Function to prevent circular folder references
CREATE OR REPLACE FUNCTION prevent_circular_folder_reference()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the new parent would create a circular reference
  IF NEW.parent_folder_id IS NOT NULL THEN
    -- Check if the new parent is a descendant of this folder
    IF EXISTS (
      SELECT 1 FROM folders 
      WHERE id = NEW.parent_folder_id 
      AND (path LIKE (SELECT path FROM folders WHERE id = NEW.id) || '%')
    ) THEN
      RAISE EXCEPTION 'Cannot move folder: would create circular reference';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent circular references
CREATE TRIGGER trigger_prevent_circular_folder_reference
  BEFORE UPDATE ON folders
  FOR EACH ROW
  EXECUTE FUNCTION prevent_circular_folder_reference();
