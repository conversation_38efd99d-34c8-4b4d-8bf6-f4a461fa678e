import React, { createContext, useContext, useEffect, useState } from "react";
import { authService, type AuthUser } from "../lib/auth";
import { profileService } from "../lib/profileService";
import toast from "react-hot-toast";

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (data: {
    email: string;
    password: string;
    fullName: string;
    role: "student" | "lecturer";
    institution?: string;
  }) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set a timeout to ensure loading doesn't hang forever
    const timeoutId = setTimeout(() => {
      console.warn("Auth initialization timeout, setting loading to false");
      setLoading(false);
    }, 5000); // 5 second timeout

    // Get initial user
    authService
      .getCurrentUser()
      .then((user) => {
        clearTimeout(timeoutId);
        setUser(user);
        setLoading(false);
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        console.error("Error getting initial user:", error);
        setUser(null);
        setLoading(false);
      });

    // Listen for auth changes
    const {
      data: { subscription },
    } = authService.onAuthStateChange(async (user) => {
      console.log("AuthContext received user:", user?.email, user?.profile);

      // Ensure user has a profile (fallback if database trigger fails)
      if (user?.id && user?.email) {
        try {
          await profileService.ensureUserProfile({
            id: user.id,
            email: user.email,
            user_metadata: {
              full_name: user.profile?.full_name,
              role: user.profile?.role,
              institution: user.profile?.institution,
            },
          } as any);
        } catch (error) {
          console.warn("Failed to ensure user profile:", error);
          // Don't block user session for profile creation failures
        }
      }

      setUser(user);
      setLoading(false);
    });

    return () => {
      clearTimeout(timeoutId);
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      await authService.signIn({ email, password });
      toast.success("Welcome back!");
    } catch (error: any) {
      toast.error(error.message || "Failed to sign in");
      throw error;
    }
  };

  const signUp = async (data: {
    email: string;
    password: string;
    fullName: string;
    role: "student" | "lecturer";
    institution?: string;
  }) => {
    try {
      setLoading(true);
      await authService.signUp(data);
      toast.success(
        "Account created! Please check your email to verify your account."
      );
    } catch (error: any) {
      toast.error(error.message || "Failed to create account");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await authService.signOut();
      // Clear user state immediately
      setUser(null);
      toast.success("Signed out successfully");
    } catch (error: any) {
      toast.error(error.message || "Failed to sign out");
      throw error;
    }
  };

  const resetPassword = async (email: string) => {
    try {
      await authService.resetPassword(email);
      toast.success("Password reset email sent!");
    } catch (error: any) {
      toast.error(error.message || "Failed to send reset email");
      throw error;
    }
  };

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
