import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { FolderPlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import {
  folderService,
  type CreateFolderData,
  type UpdateFolderData,
  type Folder,
} from "../../lib/folderService";
import { useAuth } from "../../contexts/AuthContext";
import toast from "react-hot-toast";

const folderSchema = z.object({
  name: z
    .string()
    .min(1, "Folder name is required")
    .max(255, "Folder name is too long")
    .refine(
      (name) => !/[<>:"/\\|?*]/.test(name),
      "Folder name contains invalid characters"
    ),
  description: z.string().max(1000, "Description is too long").optional(),
});

type FolderFormData = z.infer<typeof folderSchema>;

interface FolderActionsProps {
  currentFolderId?: string | null;
  onFolderCreated?: (folder: Folder) => void;
  onFolderUpdated?: (folder: Folder) => void;
  editingFolder?: Folder | null;
  onCancelEdit?: () => void;
  className?: string;
}

export function FolderActions({
  currentFolderId,
  onFolderCreated,
  onFolderUpdated,
  editingFolder,
  onCancelEdit,
  className = "",
}: FolderActionsProps) {
  const { user } = useAuth();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FolderFormData>({
    resolver: zodResolver(folderSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // Set form values when editing folder changes
  React.useEffect(() => {
    if (editingFolder) {
      setValue("name", editingFolder.name);
      setValue("description", editingFolder.description || "");
      setShowCreateForm(true);
    } else {
      reset();
      setShowCreateForm(false);
    }
  }, [editingFolder, setValue, reset]);

  const onSubmit = async (data: FolderFormData) => {
    if (!user) {
      toast.error("You must be logged in to create folders");
      return;
    }

    setIsLoading(true);
    try {
      if (editingFolder) {
        // Update existing folder
        const updateData: UpdateFolderData = {
          name: data.name,
          description: data.description,
        };
        const updatedFolder = await folderService.updateFolder(
          editingFolder.id,
          updateData,
          user.id
        );
        toast.success("Folder updated successfully");
        onFolderUpdated?.(updatedFolder);
        onCancelEdit?.();
      } else {
        // Create new folder
        const createData: CreateFolderData = {
          name: data.name,
          description: data.description,
          parent_folder_id: currentFolderId || undefined,
        };
        const newFolder = await folderService.createFolder(createData, user.id);
        toast.success("Folder created successfully");
        onFolderCreated?.(newFolder);
        setShowCreateForm(false);
      }
      reset();
    } catch (error: any) {
      console.error("Folder operation error:", error);
      toast.error(error.message || "Failed to save folder");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (editingFolder) {
      onCancelEdit?.();
    } else {
      setShowCreateForm(false);
    }
    reset();
  };

  return (
    <div className={className}>
      {!showCreateForm && !editingFolder && (
        <button
          onClick={() => setShowCreateForm(true)}
          className="btn-secondary flex items-center space-x-2"
        >
          <FolderPlusIcon className="h-5 w-5" />
          <span>New Folder</span>
        </button>
      )}

      {(showCreateForm || editingFolder) && (
        <div className="card p-4 border-2 border-blue-200 bg-blue-50">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              {editingFolder ? "Edit Folder" : "Create New Folder"}
            </h3>
            <button
              onClick={handleCancel}
              className="p-1 rounded-full hover:bg-gray-200 transition-colors duration-200"
            >
              <XMarkIcon className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label
                htmlFor="folder-name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Folder Name *
              </label>
              <input
                id="folder-name"
                type="text"
                {...register("name")}
                className="input-field"
                placeholder="Enter folder name"
                autoFocus
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="folder-description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description (Optional)
              </label>
              <textarea
                id="folder-description"
                {...register("description")}
                rows={3}
                className="input-field resize-none"
                placeholder="Enter folder description"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.description.message}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <button
                type="submit"
                disabled={isLoading}
                className="btn-primary flex items-center space-x-2"
              >
                <FolderPlusIcon className="h-4 w-4" />
                <span>
                  {isLoading
                    ? "Saving..."
                    : editingFolder
                    ? "Update Folder"
                    : "Create Folder"}
                </span>
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="btn-secondary"
                disabled={isLoading}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
