import { supabase } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface AuthUser extends User {
  profile?: {
    full_name: string | null;
    avatar_url: string | null;
    role: "student" | "lecturer" | "admin";
    institution: string | null;
  };
}

export interface SignUpData {
  email: string;
  password: string;
  fullName: string;
  role: "student" | "lecturer";
  institution?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

// Authentication functions
export const authService = {
  // Sign up new user
  async signUp(data: SignUpData) {
    const { email, password, fullName, role, institution } = data;

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          role,
          institution,
        },
      },
    });

    if (authError) throw authError;

    // Try to create profile record, but don't fail if table doesn't exist
    if (authData.user) {
      try {
        const { error: profileError } = await supabase.from("profiles").insert({
          id: authData.user.id,
          email: authData.user.email!,
          full_name: fullName,
          role,
          institution,
        });

        // Only throw error if it's not a "table doesn't exist" error
        if (profileError && !profileError.message.includes("does not exist")) {
          throw profileError;
        }
      } catch (error: any) {
        // Log the error but don't fail registration
        console.warn(
          "Profile creation failed (table may not exist):",
          error.message
        );
      }
    }

    return authData;
  },

  // Sign in user
  async signIn(data: SignInData) {
    const { email, password } = data;

    const { data: authData, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return authData;
  },

  // Sign out user
  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    if (error) throw error;
  },

  // Update password
  async updatePassword(password: string) {
    const { error } = await supabase.auth.updateUser({
      password,
    });
    if (error) throw error;
  },

  // Get current user
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      console.log("getCurrentUser: Fetching user...");

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error("getCurrentUser timeout")), 3000);
      });

      const getUserPromise = supabase.auth.getUser();

      const {
        data: { user },
      } = await Promise.race([getUserPromise, timeoutPromise]);

      console.log(
        "getCurrentUser: Raw user data:",
        user?.email,
        user?.user_metadata
      );

      if (!user) {
        console.log("getCurrentUser: No user found");
        return null;
      }

      // Always use user metadata as fallback since profiles table may not exist
      const profile = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || null,
        role: user.user_metadata?.role || "student",
        institution: user.user_metadata?.institution || null,
        avatar_url: null,
        created_at: user.created_at,
        updated_at: user.updated_at || user.created_at,
      };

      const authUser = {
        ...user,
        profile,
      } as AuthUser;

      console.log(
        "getCurrentUser: Returning user:",
        authUser.email,
        authUser.profile
      );
      return authUser;
    } catch (error: any) {
      console.error("Error in getCurrentUser:", error);
      return null;
    }
  },

  // Get auth state change listener
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state change:", event, session?.user?.email);

      if (session?.user) {
        try {
          // Use session data directly instead of calling getCurrentUser
          const user = session.user;
          const profile = {
            id: user.id,
            email: user.email,
            full_name: user.user_metadata?.full_name || null,
            role: user.user_metadata?.role || "student",
            institution: user.user_metadata?.institution || null,
            avatar_url: null,
            created_at: user.created_at,
            updated_at: user.updated_at || user.created_at,
          };

          const authUser = {
            ...user,
            profile,
          } as AuthUser;

          console.log(
            "Auth state change - created user:",
            authUser.email,
            authUser.profile
          );
          callback(authUser);
        } catch (error) {
          console.error("Error creating user from session:", error);
          callback(null);
        }
      } else {
        console.log("No session, setting user to null");
        callback(null);
      }
    });
  },
};
