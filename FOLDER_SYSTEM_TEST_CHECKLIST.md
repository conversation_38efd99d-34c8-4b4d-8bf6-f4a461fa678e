# SmartBagPack Folder System Test Checklist

## Pre-Testing Setup
- [ ] Run database migration script (`database/folder-system-migration.sql`)
- [ ] Verify all tables and policies are created
- [ ] Ensure application builds without errors
- [ ] Test with a clean user account

## Basic Folder Operations

### Folder Creation
- [ ] Create a new folder from Documents page
- [ ] Verify folder appears in folder list
- [ ] Create folder with special characters (should be prevented)
- [ ] Create folder with very long name (should be limited)
- [ ] Create duplicate folder name in same location (should be prevented)

### Folder Navigation
- [ ] Click on folder to navigate into it
- [ ] Verify breadcrumb shows current path
- [ ] Navigate back using breadcrumb links
- [ ] Navigate to root using "My Documents" breadcrumb

### Folder Management
- [ ] Rename a folder using the actions menu
- [ ] Try to rename folder to existing name (should be prevented)
- [ ] Delete an empty folder
- [ ] Try to delete folder with contents (should be prevented)
- [ ] Move folder to different parent folder

## Document Operations

### Document Upload
- [ ] Upload document to root (no folder selected)
- [ ] Upload document to specific folder using Upload page
- [ ] Verify document appears in correct folder
- [ ] Upload multiple documents to same folder

### Document Organization
- [ ] View documents in specific folder
- [ ] Verify documents in subfolders don't appear in parent
- [ ] Check that root documents still appear when no folder selected

### Document Movement
- [ ] Drag document from file list to folder (if drag-and-drop enabled)
- [ ] Move document between different folders
- [ ] Move document from folder back to root
- [ ] Move multiple documents at once

## Drag and Drop (Desktop Only)

### Document Dragging
- [ ] Drag document card shows visual feedback
- [ ] Drop target folders highlight on hover
- [ ] Document moves to correct folder after drop
- [ ] Invalid drop targets are rejected

### Folder Dragging
- [ ] Drag folder to move it into another folder
- [ ] Prevent dropping folder onto itself
- [ ] Prevent circular references (folder into its own subfolder)
- [ ] Verify folder hierarchy updates correctly

## User Interface

### Responsive Design
- [ ] Test on mobile device (drag-and-drop should be disabled)
- [ ] Verify folder list is readable on small screens
- [ ] Check breadcrumb navigation on mobile
- [ ] Ensure folder actions menu works on touch devices

### View Modes
- [ ] Switch between grid and list view for folders
- [ ] Verify both view modes show folder information correctly
- [ ] Check that document counts are accurate
- [ ] Test folder actions in both view modes

### Visual Feedback
- [ ] Folder icons display correctly
- [ ] Document counts show accurate numbers
- [ ] Loading states appear during operations
- [ ] Success/error messages display appropriately

## Integration Testing

### Document Sharing
- [ ] Share document that's in a folder
- [ ] Verify shared document is accessible to recipient
- [ ] Check that folder location doesn't affect sharing
- [ ] Test sharing documents from different folder levels

### Room Sharing
- [ ] Share folder documents to rooms
- [ ] Verify room members can access shared documents
- [ ] Check that folder organization is maintained in room context

### Search and Filters
- [ ] Search for documents within specific folders
- [ ] Apply filters to folder contents
- [ ] Verify search works across folder hierarchy
- [ ] Test that folder navigation preserves search state

### Statistics
- [ ] Verify document counts include folder contents
- [ ] Check storage usage calculations
- [ ] Ensure statistics update after folder operations

## Error Handling

### Validation Errors
- [ ] Empty folder name shows appropriate error
- [ ] Invalid characters in folder name are rejected
- [ ] Duplicate folder names show clear error message
- [ ] Network errors display user-friendly messages

### Edge Cases
- [ ] Very deep folder nesting (10+ levels)
- [ ] Folders with very long names
- [ ] Operations on folders with many documents
- [ ] Concurrent operations by multiple users

## Performance Testing

### Load Testing
- [ ] Create 50+ folders and verify performance
- [ ] Upload 100+ documents across various folders
- [ ] Test navigation with large folder hierarchies
- [ ] Verify drag-and-drop performance with many items

### Database Performance
- [ ] Check query performance in browser network tab
- [ ] Monitor database load during folder operations
- [ ] Verify indexes are being used effectively

## Security Testing

### Access Control
- [ ] Verify users can only see their own folders
- [ ] Test that folder operations respect user ownership
- [ ] Ensure shared documents maintain proper access controls
- [ ] Check that folder deletion doesn't affect other users

### Data Integrity
- [ ] Verify folder paths are updated correctly
- [ ] Check that document-folder relationships are maintained
- [ ] Ensure database constraints prevent invalid states
- [ ] Test rollback behavior on failed operations

## Browser Compatibility

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Browsers
- [ ] Mobile Chrome
- [ ] Mobile Safari
- [ ] Mobile Firefox

## Final Integration Test

### Complete Workflow
- [ ] Create folder structure (3-4 levels deep)
- [ ] Upload documents to various folders
- [ ] Move documents between folders
- [ ] Share some documents with other users
- [ ] Share some documents to rooms
- [ ] Rename and reorganize folders
- [ ] Delete empty folders
- [ ] Verify all operations completed successfully

### Data Consistency
- [ ] Refresh page and verify folder structure persists
- [ ] Log out and log back in to verify data integrity
- [ ] Check that all document relationships are maintained
- [ ] Verify breadcrumb navigation reflects actual structure

## Post-Testing Cleanup
- [ ] Document any bugs or issues found
- [ ] Clean up test data if needed
- [ ] Verify system performance after testing
- [ ] Update documentation based on test results

---

## Notes Section
Use this space to record any issues, observations, or improvements discovered during testing:

**Issues Found:**
- 

**Performance Observations:**
- 

**UI/UX Feedback:**
- 

**Suggested Improvements:**
- 
