import { supabase } from "./supabase";

export interface Folder {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  parent_folder_id?: string;
  path: string;
  created_at: string;
  updated_at: string;
}

export interface FolderWithChildren extends Folder {
  children?: FolderWithChildren[];
  document_count?: number;
}

export interface CreateFolderData {
  name: string;
  description?: string;
  parent_folder_id?: string;
}

export interface UpdateFolderData {
  name?: string;
  description?: string;
  parent_folder_id?: string;
}

export const folderService = {
  // Create a new folder
  async createFolder(data: CreateFolderData, userId: string): Promise<Folder> {
    try {
      // Validate folder name
      if (!data.name.trim()) {
        throw new Error("Folder name is required");
      }

      if (data.name.length > 255) {
        throw new Error("Folder name is too long");
      }

      // Check for invalid characters in folder name
      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(data.name)) {
        throw new Error("Folder name contains invalid characters");
      }

      // If parent folder is specified, verify it exists and belongs to user
      if (data.parent_folder_id) {
        const { data: parentFolder, error: parentError } = await supabase
          .from("folders")
          .select("id, user_id")
          .eq("id", data.parent_folder_id)
          .eq("user_id", userId)
          .single();

        if (parentError || !parentFolder) {
          throw new Error("Parent folder not found or access denied");
        }
      }

      // Check for duplicate folder name in the same parent
      const { data: existingFolder } = await supabase
        .from("folders")
        .select("id")
        .eq("user_id", userId)
        .eq("name", data.name.trim())
        .eq("parent_folder_id", data.parent_folder_id || null)
        .single();

      if (existingFolder) {
        throw new Error(
          "A folder with this name already exists in this location"
        );
      }

      // Create the folder
      const { data: folder, error } = await supabase
        .from("folders")
        .insert({
          user_id: userId,
          name: data.name.trim(),
          description: data.description?.trim() || null,
          parent_folder_id: data.parent_folder_id || null,
          path: "", // Will be set by trigger
        })
        .select()
        .single();

      if (error) throw error;
      return folder;
    } catch (error: any) {
      console.error("Create folder error:", error);
      throw new Error(error.message || "Failed to create folder");
    }
  },

  // Get user's folders
  async getUserFolders(userId: string): Promise<Folder[]> {
    try {
      const { data, error } = await supabase
        .from("folders")
        .select("*")
        .eq("user_id", userId)
        .order("name");

      if (error) throw error;
      return data || [];
    } catch (error: any) {
      console.error("Get user folders error:", error);
      throw new Error(error.message || "Failed to fetch folders");
    }
  },

  // Alias for getUserFolders - used by FolderOperationsModal
  async getAllFolders(userId: string): Promise<Folder[]> {
    return this.getUserFolders(userId);
  },

  // Get folder by ID
  async getFolderById(
    folderId: string,
    userId: string
  ): Promise<Folder | null> {
    try {
      const { data, error } = await supabase
        .from("folders")
        .select("*")
        .eq("id", folderId)
        .eq("user_id", userId)
        .single();

      if (error) {
        if (error.code === "PGRST116") return null; // Not found
        throw error;
      }
      return data;
    } catch (error: any) {
      console.error("Get folder by ID error:", error);
      throw new Error(error.message || "Failed to fetch folder");
    }
  },

  // Get folder hierarchy (folders with their children)
  async getFolderHierarchy(
    userId: string,
    parentId?: string | null
  ): Promise<FolderWithChildren[]> {
    try {
      // Get folders at the specified level
      let query = supabase
        .from("folders")
        .select(
          `
          *,
          documents(count)
        `
        )
        .eq("user_id", userId);

      // Handle null/undefined parentId properly
      if (parentId === undefined || parentId === null) {
        query = query.is("parent_folder_id", null);
      } else {
        query = query.eq("parent_folder_id", parentId);
      }

      const { data: folders, error } = await query.order("name");

      if (error) throw error;

      // Transform the data to include document count
      const foldersWithCount: FolderWithChildren[] = (folders || []).map(
        (folder) => ({
          ...folder,
          document_count: folder.documents?.[0]?.count || 0,
        })
      );

      return foldersWithCount;
    } catch (error: any) {
      console.error("Get folder hierarchy error:", error);
      throw new Error(error.message || "Failed to fetch folder hierarchy");
    }
  },

  // Get folder breadcrumb path
  async getFolderBreadcrumb(
    folderId: string,
    userId: string
  ): Promise<Folder[]> {
    try {
      const folder = await this.getFolderById(folderId, userId);
      if (!folder) return [];

      // Parse the path to get all parent folder IDs
      const pathParts = folder.path
        .split("/")
        .filter((part) => part.length > 0);

      if (pathParts.length === 0) return [folder];

      // Get all folders in the path
      const { data: pathFolders, error } = await supabase
        .from("folders")
        .select("*")
        .eq("user_id", userId)
        .in("name", pathParts)
        .order("path");

      if (error) throw error;

      return pathFolders || [];
    } catch (error: any) {
      console.error("Get folder breadcrumb error:", error);
      throw new Error(error.message || "Failed to fetch folder breadcrumb");
    }
  },

  // Update folder
  async updateFolder(
    folderId: string,
    data: UpdateFolderData,
    userId: string
  ): Promise<Folder> {
    try {
      // Verify folder exists and belongs to user
      const existingFolder = await this.getFolderById(folderId, userId);
      if (!existingFolder) {
        throw new Error("Folder not found or access denied");
      }

      // Validate name if provided
      if (data.name !== undefined) {
        if (!data.name.trim()) {
          throw new Error("Folder name is required");
        }

        if (data.name.length > 255) {
          throw new Error("Folder name is too long");
        }

        const invalidChars = /[<>:"/\\|?*]/;
        if (invalidChars.test(data.name)) {
          throw new Error("Folder name contains invalid characters");
        }

        // Check for duplicate name in same parent (if name is changing)
        if (data.name.trim() !== existingFolder.name) {
          const { data: duplicateFolder } = await supabase
            .from("folders")
            .select("id")
            .eq("user_id", userId)
            .eq("name", data.name.trim())
            .eq(
              "parent_folder_id",
              data.parent_folder_id ?? existingFolder.parent_folder_id
            )
            .neq("id", folderId)
            .single();

          if (duplicateFolder) {
            throw new Error(
              "A folder with this name already exists in this location"
            );
          }
        }
      }

      // If moving folder, verify parent exists and prevent circular reference
      if (
        data.parent_folder_id !== undefined &&
        data.parent_folder_id !== existingFolder.parent_folder_id
      ) {
        if (data.parent_folder_id) {
          const { data: parentFolder, error: parentError } = await supabase
            .from("folders")
            .select("id, path")
            .eq("id", data.parent_folder_id)
            .eq("user_id", userId)
            .single();

          if (parentError || !parentFolder) {
            throw new Error("Parent folder not found or access denied");
          }

          // Check for circular reference
          if (parentFolder.path.startsWith(existingFolder.path)) {
            throw new Error(
              "Cannot move folder: would create circular reference"
            );
          }
        }
      }

      // Update the folder
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name.trim();
      if (data.description !== undefined)
        updateData.description = data.description?.trim() || null;
      if (data.parent_folder_id !== undefined)
        updateData.parent_folder_id = data.parent_folder_id;

      const { data: updatedFolder, error } = await supabase
        .from("folders")
        .update(updateData)
        .eq("id", folderId)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) throw error;
      return updatedFolder;
    } catch (error: any) {
      console.error("Update folder error:", error);
      throw new Error(error.message || "Failed to update folder");
    }
  },

  // Delete folder
  async deleteFolder(folderId: string, userId: string): Promise<void> {
    try {
      // Verify folder exists and belongs to user
      const folder = await this.getFolderById(folderId, userId);
      if (!folder) {
        throw new Error("Folder not found or access denied");
      }

      // Check if folder has children
      const { data: children, error: childrenError } = await supabase
        .from("folders")
        .select("id")
        .eq("parent_folder_id", folderId)
        .limit(1);

      if (childrenError) throw childrenError;

      if (children && children.length > 0) {
        throw new Error("Cannot delete folder: it contains subfolders");
      }

      // Check if folder has documents
      const { data: documents, error: documentsError } = await supabase
        .from("documents")
        .select("id")
        .eq("folder_id", folderId)
        .limit(1);

      if (documentsError) throw documentsError;

      if (documents && documents.length > 0) {
        throw new Error("Cannot delete folder: it contains documents");
      }

      // Delete the folder
      const { error } = await supabase
        .from("folders")
        .delete()
        .eq("id", folderId)
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Delete folder error:", error);
      throw new Error(error.message || "Failed to delete folder");
    }
  },

  // Move documents to folder
  async moveDocumentsToFolder(
    documentIds: string[],
    folderId: string | null,
    userId: string
  ): Promise<void> {
    try {
      // If moving to a folder, verify it exists and belongs to user
      if (folderId) {
        const folder = await this.getFolderById(folderId, userId);
        if (!folder) {
          throw new Error("Target folder not found or access denied");
        }
      }

      // Update documents
      const { error } = await supabase
        .from("documents")
        .update({ folder_id: folderId })
        .in("id", documentIds)
        .eq("user_id", userId);

      if (error) throw error;
    } catch (error: any) {
      console.error("Move documents to folder error:", error);
      throw new Error(error.message || "Failed to move documents");
    }
  },

  // Copy folder (creates a duplicate with all its contents)
  async copyFolder(
    folderId: string,
    targetParentId: string | null,
    userId: string,
    newName?: string
  ): Promise<Folder> {
    try {
      // Get the source folder
      const sourceFolder = await this.getFolderById(folderId, userId);
      if (!sourceFolder) {
        throw new Error("Source folder not found");
      }

      // Validate target parent if specified
      if (targetParentId) {
        const targetParent = await this.getFolderById(targetParentId, userId);
        if (!targetParent) {
          throw new Error("Target parent folder not found");
        }

        // Prevent copying into itself or its descendants
        if (targetParentId === folderId) {
          throw new Error("Cannot copy folder into itself");
        }

        // Check if target is a descendant of source
        const targetPath = targetParent.path;
        const sourcePath = sourceFolder.path;
        if (targetPath.startsWith(sourcePath + sourceFolder.id + "/")) {
          throw new Error("Cannot copy folder into its own descendant");
        }
      }

      // Create the new folder
      const folderName = newName || `${sourceFolder.name} (Copy)`;
      const { data: newFolder, error } = await supabase
        .from("folders")
        .insert({
          user_id: userId,
          name: folderName,
          description: sourceFolder.description,
          parent_folder_id: targetParentId,
          path: "", // Will be set by trigger
        })
        .select()
        .single();

      if (error) throw error;

      // TODO: Copy all documents and subfolders recursively
      // This would require additional implementation for document copying

      return newFolder;
    } catch (error: any) {
      console.error("Copy folder error:", error);
      throw new Error(error.message || "Failed to copy folder");
    }
  },

  // Move folder to a different parent
  async moveFolder(
    folderId: string,
    targetParentId: string | null,
    userId: string
  ): Promise<Folder> {
    try {
      // Get the source folder
      const sourceFolder = await this.getFolderById(folderId, userId);
      if (!sourceFolder) {
        throw new Error("Source folder not found");
      }

      // Validate target parent if specified
      if (targetParentId) {
        const targetParent = await this.getFolderById(targetParentId, userId);
        if (!targetParent) {
          throw new Error("Target parent folder not found");
        }

        // Prevent moving into itself
        if (targetParentId === folderId) {
          throw new Error("Cannot move folder into itself");
        }

        // Check if target is a descendant of source
        const targetPath = targetParent.path;
        const sourcePath = sourceFolder.path;
        if (targetPath.startsWith(sourcePath + sourceFolder.id + "/")) {
          throw new Error("Cannot move folder into its own descendant");
        }
      }

      // Update the folder's parent
      const { data: updatedFolder, error } = await supabase
        .from("folders")
        .update({ parent_folder_id: targetParentId })
        .eq("id", folderId)
        .eq("user_id", userId)
        .select()
        .single();

      if (error) throw error;
      return updatedFolder;
    } catch (error: any) {
      console.error("Move folder error:", error);
      throw new Error(error.message || "Failed to move folder");
    }
  },

  // Create subfolder within an existing folder
  async createSubfolder(
    parentFolderId: string,
    data: CreateFolderData,
    userId: string
  ): Promise<Folder> {
    try {
      // Verify parent folder exists and belongs to user
      const parentFolder = await this.getFolderById(parentFolderId, userId);
      if (!parentFolder) {
        throw new Error("Parent folder not found or access denied");
      }

      // Create the subfolder
      return await this.createFolder(
        {
          ...data,
          parent_folder_id: parentFolderId,
        },
        userId
      );
    } catch (error: any) {
      console.error("Create subfolder error:", error);
      throw new Error(error.message || "Failed to create subfolder");
    }
  },

  // Get folder statistics (document count, total size, etc.)
  async getFolderStatistics(
    folderId: string,
    userId: string
  ): Promise<{
    documentCount: number;
    totalSize: number;
    subfolderCount: number;
  }> {
    try {
      // Get document count and total size
      const { data: documents, error: docsError } = await supabase
        .from("documents")
        .select("file_size")
        .eq("folder_id", folderId)
        .eq("user_id", userId);

      if (docsError) throw docsError;

      // Get subfolder count
      const { count: subfolderCount, error: foldersError } = await supabase
        .from("folders")
        .select("*", { count: "exact", head: true })
        .eq("parent_folder_id", folderId)
        .eq("user_id", userId);

      if (foldersError) throw foldersError;

      const documentCount = documents?.length || 0;
      const totalSize =
        documents?.reduce((sum, doc) => sum + (doc.file_size || 0), 0) || 0;

      return {
        documentCount,
        totalSize,
        subfolderCount: subfolderCount || 0,
      };
    } catch (error: any) {
      console.error("Get folder statistics error:", error);
      throw new Error(error.message || "Failed to get folder statistics");
    }
  },
};
