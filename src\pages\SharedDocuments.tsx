import { useState, useEffect } from "react";
import { DashboardLayout } from "../components/layout/DashboardLayout";
import { FileList } from "../components/files/FileList";
import { fileService } from "../lib/fileService";
import type { DocumentFile } from "../lib/fileService";
import { useAuth } from "../contexts/AuthContext";
import toast from "react-hot-toast";
import { formatFileSize } from "../lib/utils";
import { supabase } from "../lib/supabase";
import { usePageTitle } from "../hooks/usePageTitle";

interface SharedDocumentWithInfo extends DocumentFile {
  sharing_info: {
    is_shared_with_me: boolean;
    shared_by_user: string;
    shared_by_name: string;
    permission: "view" | "download";
    share_type: "user" | "room";
    shared_at: string;
    expires_at?: string | null;
  };
}

interface SharedByMeDocument extends DocumentFile {
  shares: Array<{
    id: string;
    shared_with: string;
    shared_with_name: string;
    permission: "view" | "download";
    created_at: string;
    expires_at?: string | null;
  }>;
}

export function SharedDocuments() {
  const { user } = useAuth();

  // Set page title
  usePageTitle("Shared Documents");

  const [activeTab, setActiveTab] = useState<"shared-with-me" | "shared-by-me">(
    "shared-with-me"
  );
  const [sharedWithMe, setSharedWithMe] = useState<SharedDocumentWithInfo[]>(
    []
  );
  const [sharedByMe, setSharedByMe] = useState<SharedByMeDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    receivedDocuments: 0,
    sharedDocuments: 0,
    totalShares: 0,
    activeShares: 0,
  });

  useEffect(() => {
    if (user) {
      loadSharedDocuments();
      loadStatistics();
    }
  }, [user]);

  const loadSharedDocuments = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Load documents shared with me
      const [userSharedDocs, roomSharedDocs] = await Promise.all([
        fileService.getSharedDocumentsWithDetails(user.id),
        loadRoomSharedDocuments(),
      ]);

      // Format documents shared with me
      const formattedSharedWithMe: SharedDocumentWithInfo[] = [
        ...userSharedDocs.map((doc) => ({
          ...doc,
          sharing_info: {
            is_shared_with_me: true,
            shared_by_user: doc.share_info.shared_by,
            shared_by_name:
              doc.share_info.sharer_profile?.full_name ||
              doc.share_info.sharer_profile?.email ||
              "Unknown",
            permission: doc.share_info.permission,
            share_type: "user" as const,
            shared_at: doc.share_info.created_at,
            expires_at: doc.share_info.expires_at,
          },
        })),
        ...roomSharedDocs,
      ];

      setSharedWithMe(formattedSharedWithMe);

      // Load documents shared by me
      const myDocuments = await fileService.getUserDocuments(user.id);
      const documentsWithShares = await Promise.all(
        myDocuments.map(async (doc) => {
          const shares = await fileService.getDocumentShares(doc.id, user.id);
          return {
            ...doc,
            shares: shares.map((share) => ({
              id: share.id,
              shared_with: share.shared_with,
              shared_with_name:
                (share as any).shared_with_profile?.full_name ||
                (share as any).shared_with_profile?.email ||
                "Unknown",
              permission: share.permission,
              created_at: share.created_at,
              expires_at: share.expires_at,
            })),
          };
        })
      );

      // Filter only documents that have shares
      const sharedByMeFiltered = documentsWithShares.filter(
        (doc) => doc.shares.length > 0
      );
      setSharedByMe(sharedByMeFiltered);
    } catch (error: any) {
      console.error("Load shared documents error:", error);
      toast.error("Failed to load shared documents");
    } finally {
      setIsLoading(false);
    }
  };

  const loadRoomSharedDocuments = async (): Promise<
    SharedDocumentWithInfo[]
  > => {
    if (!user) return [];

    try {
      // Get user's room memberships
      const { data: memberships, error: membershipError } = await supabase
        .from("room_members")
        .select("room_id, rooms(name)")
        .eq("user_id", user.id);

      if (membershipError) {
        console.error("Error fetching room memberships:", membershipError);
        return [];
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      const roomIds = memberships.map((m) => m.room_id);

      // Get room documents
      const { data: roomDocs, error: roomDocsError } = await supabase
        .from("room_documents")
        .select(
          `
          *,
          documents(*),
          profiles!shared_by(full_name, email)
        `
        )
        .in("room_id", roomIds)
        .neq("shared_by", user.id); // Don't include documents shared by the user themselves

      if (roomDocsError) {
        console.error("Error fetching room documents:", roomDocsError);
        return [];
      }

      if (!roomDocs) return [];

      return roomDocs.map((roomDoc) => ({
        ...roomDoc.documents,
        sharing_info: {
          is_shared_with_me: true,
          shared_by_user: roomDoc.shared_by,
          shared_by_name:
            roomDoc.profiles?.full_name || roomDoc.profiles?.email || "Unknown",
          permission: roomDoc.permission,
          share_type: "room" as const,
          shared_at: roomDoc.shared_at,
          expires_at: null,
        },
      }));
    } catch (error) {
      console.error("Load room shared documents error:", error);
      return [];
    }
  };

  const loadStatistics = async () => {
    if (!user) return;

    try {
      // Count documents shared with me - but only those that actually exist and are accessible
      const { data: userShares, error: userSharesError } = await supabase
        .from("document_shares")
        .select("document_id")
        .eq("shared_with", user.id);

      if (userSharesError) {
        console.error("Error fetching user shares:", userSharesError);
      }

      let receivedCount = 0;
      if (userShares && userShares.length > 0) {
        // Check which documents actually exist and are accessible
        const documentIds = userShares.map((share) => share.document_id);
        const { data: existingDocs, error: docsError } = await supabase
          .from("documents")
          .select("id")
          .in("id", documentIds);

        if (docsError) {
          console.error("Error checking existing documents:", docsError);
        }

        receivedCount = existingDocs?.length || 0;
      }

      // Count room documents shared with me
      const { data: roomMemberships } = await supabase
        .from("room_members")
        .select("room_id")
        .eq("user_id", user.id);

      let roomReceivedCount = 0;
      if (roomMemberships && roomMemberships.length > 0) {
        const roomIds = roomMemberships.map((m) => m.room_id);
        const { count: roomDocsCount } = await supabase
          .from("room_documents")
          .select("*", { count: "exact", head: true })
          .in("room_id", roomIds)
          .neq("shared_by", user.id);

        roomReceivedCount = roomDocsCount || 0;
      }

      // Count documents I've shared
      const { count: sharedCount } = await supabase
        .from("document_shares")
        .select("*", { count: "exact", head: true })
        .eq("shared_by", user.id);

      // Total received documents (user shares + room shares)
      const totalReceived = receivedCount + roomReceivedCount;

      // Count total shares (both directions)
      const totalShares = totalReceived + (sharedCount || 0);

      // Count active shares (non-expired)
      const { count: activeCount } = await supabase
        .from("document_shares")
        .select("*", { count: "exact", head: true })
        .or(`shared_with.eq.${user.id},shared_by.eq.${user.id}`)
        .or("expires_at.is.null,expires_at.gt.now()");

      setStatistics({
        receivedDocuments: totalReceived,
        sharedDocuments: sharedCount || 0,
        totalShares,
        activeShares: activeCount || 0,
      });
    } catch (error) {
      console.error("Load statistics error:", error);
    }
  };

  const handleRevokeShare = async (shareId: string) => {
    try {
      await fileService.revokeDocumentShare(shareId, user!.id);
      toast.success("Share revoked successfully");
      loadSharedDocuments();
      loadStatistics();
    } catch (error: any) {
      toast.error(error.message || "Failed to revoke share");
    }
  };

  const tabs = [
    {
      id: "shared-with-me",
      name: "Shared with Me",
      count: sharedWithMe.length,
    },
    {
      id: "shared-by-me",
      name: "Shared by Me",
      count: sharedByMe.length,
    },
  ];

  return (
    <DashboardLayout>
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900">
              Shared Documents
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage documents shared with you and documents you've shared with
              others.
            </p>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Received Documents
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {isLoading ? "..." : statistics.receivedDocuments}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Documents Shared
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {isLoading ? "..." : statistics.sharedDocuments}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total Shares
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {isLoading ? "..." : statistics.totalShares}
                  </p>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-orange-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Active Shares
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {isLoading ? "..." : statistics.activeShares}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`
                      py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap
                      ${
                        activeTab === tab.id
                          ? "border-blue-500 text-blue-600"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }
                    `}
                  >
                    {tab.name}
                    <span
                      className={`
                        ml-2 py-0.5 px-2 rounded-full text-xs
                        ${
                          activeTab === tab.id
                            ? "bg-blue-100 text-blue-600"
                            : "bg-gray-100 text-gray-900"
                        }
                      `}
                    >
                      {tab.count}
                    </span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="card">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="text-gray-500">Loading shared documents...</div>
              </div>
            ) : activeTab === "shared-with-me" ? (
              <div>
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Documents Shared with Me
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Documents that other users have shared with you directly or
                    through rooms.
                  </p>
                </div>
                {sharedWithMe.length === 0 ? (
                  <div className="p-8 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No shared documents
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      No documents have been shared with you yet.
                    </p>
                  </div>
                ) : (
                  <div className="p-6">
                    <FileList
                      documents={sharedWithMe}
                      viewMode="list"
                      showActions={false}
                    />
                  </div>
                )}
              </div>
            ) : (
              <div>
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">
                    Documents Shared by Me
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Documents you have shared with other users and their sharing
                    details.
                  </p>
                </div>
                {sharedByMe.length === 0 ? (
                  <div className="p-8 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                      />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No shared documents
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      You haven't shared any documents yet.
                    </p>
                  </div>
                ) : (
                  <div className="p-6">
                    <div className="space-y-6">
                      {sharedByMe.map((document) => (
                        <div
                          key={document.id}
                          className="border border-gray-200 rounded-lg p-4"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center">
                              <div className="text-2xl mr-3">
                                {fileService.getFileIcon(document.file_type)}
                              </div>
                              <div>
                                <h4 className="font-medium text-gray-900">
                                  {document.title}
                                </h4>
                                <p className="text-sm text-gray-500">
                                  {formatFileSize(document.file_size)} • Shared
                                  with {document.shares.length} user
                                  {document.shares.length !== 1 ? "s" : ""}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h5 className="text-sm font-medium text-gray-700">
                              Shared with:
                            </h5>
                            {document.shares.map((share) => (
                              <div
                                key={share.id}
                                className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-md"
                              >
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                    <span className="text-xs font-medium text-gray-600">
                                      {share.shared_with_name
                                        .charAt(0)
                                        .toUpperCase()}
                                    </span>
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      {share.shared_with_name}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      {share.permission} access • Shared{" "}
                                      {new Date(
                                        share.created_at
                                      ).toLocaleDateString()}
                                    </p>
                                  </div>
                                </div>
                                <button
                                  onClick={() => handleRevokeShare(share.id)}
                                  className="text-red-600 hover:text-red-800 text-sm"
                                  title="Revoke access"
                                >
                                  Revoke
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
