import { supabase } from "./supabase";
import type { Database } from "./supabase";

// Type definitions
export type RoomInvitation =
  Database["public"]["Tables"]["room_invitations"]["Row"];
export type RoomInvitationLink =
  Database["public"]["Tables"]["room_invitation_links"]["Row"];

export interface InvitationWithDetails extends RoomInvitation {
  room: {
    id: string;
    name: string;
    room_code: string;
    is_private: boolean;
  };
  invited_by_profile: {
    full_name: string | null;
    email: string;
  };
  invited_user_profile: {
    full_name: string | null;
    email: string;
  };
}

export interface InvitationLinkWithDetails extends RoomInvitationLink {
  room: {
    id: string;
    name: string;
    room_code: string;
    is_private: boolean;
  };
  created_by_profile: {
    full_name: string | null;
    email: string;
  };
}

export const invitationService = {
  // Generate unique invitation token
  generateInvitationToken(): string {
    const chars =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let result = "";
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Send direct invitation to user
  async inviteUserToRoom(data: {
    roomId: string;
    invitedUserId: string;
    invitedBy: string;
    message?: string;
  }): Promise<RoomInvitation> {
    try {
      // Check if user is already a member or has pending invitation
      const { data: existingMember } = await supabase
        .from("room_members")
        .select("id")
        .eq("room_id", data.roomId)
        .eq("user_id", data.invitedUserId)
        .single();

      if (existingMember) {
        throw new Error("User is already a member of this room");
      }

      const { data: existingInvitation } = await supabase
        .from("room_invitations")
        .select("id, status")
        .eq("room_id", data.roomId)
        .eq("invited_user", data.invitedUserId)
        .single();

      if (existingInvitation) {
        if (existingInvitation.status === "pending") {
          throw new Error("User already has a pending invitation to this room");
        } else if (
          existingInvitation.status === "declined" ||
          existingInvitation.status === "expired"
        ) {
          // Update the existing invitation instead of creating a new one
          const { data: updatedInvitation, error: updateError } = await supabase
            .from("room_invitations")
            .update({
              status: "pending",
              message: data.message || null,
              expires_at: new Date(
                Date.now() + 7 * 24 * 60 * 60 * 1000
              ).toISOString(),
              updated_at: new Date().toISOString(),
            })
            .eq("id", existingInvitation.id)
            .select()
            .single();

          if (updateError) throw updateError;

          console.log(
            `Room invitation updated successfully for user ${data.invitedUserId}`
          );
          return updatedInvitation;
        } else if (existingInvitation.status === "accepted") {
          throw new Error(
            "User has already accepted an invitation to this room"
          );
        }
      }

      // Create invitation
      const { data: invitation, error } = await supabase
        .from("room_invitations")
        .insert({
          room_id: data.roomId,
          invited_by: data.invitedBy,
          invited_user: data.invitedUserId,
          message: data.message || null,
          status: "pending",
          expires_at: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000
          ).toISOString(), // 7 days
        })
        .select()
        .single();

      if (error) throw error;

      console.log(
        `Room invitation sent successfully to user ${data.invitedUserId}`
      );
      return invitation;
    } catch (error: any) {
      console.error("Send invitation error:", error);
      throw new Error(error.message || "Failed to send invitation");
    }
  },

  // Create shareable invitation link
  async createInvitationLink(data: {
    roomId: string;
    createdBy: string;
    maxUses?: number;
    expiresInDays?: number;
  }): Promise<RoomInvitationLink> {
    try {
      let linkToken = this.generateInvitationToken();

      // Ensure token is unique
      let isUnique = false;
      while (!isUnique) {
        const { data: existing } = await supabase
          .from("room_invitation_links")
          .select("id")
          .eq("link_token", linkToken)
          .single();

        if (!existing) {
          isUnique = true;
        } else {
          linkToken = this.generateInvitationToken();
        }
      }

      const expiresAt = new Date(
        Date.now() + (data.expiresInDays || 30) * 24 * 60 * 60 * 1000
      ).toISOString();

      const { data: invitationLink, error } = await supabase
        .from("room_invitation_links")
        .insert({
          room_id: data.roomId,
          created_by: data.createdBy,
          link_token: linkToken,
          max_uses: data.maxUses || null,
          current_uses: 0,
          expires_at: expiresAt,
          is_active: true,
        })
        .select()
        .single();

      if (error) throw error;

      console.log(
        `Invitation link created successfully for room ${data.roomId}`
      );
      return invitationLink;
    } catch (error: any) {
      console.error("Create invitation link error:", error);
      throw new Error(error.message || "Failed to create invitation link");
    }
  },

  // Get invitation by token (for joining via link)
  async getInvitationByToken(
    token: string
  ): Promise<InvitationLinkWithDetails | null> {
    try {
      const { data: invitationLink, error } = await supabase
        .from("room_invitation_links")
        .select(
          `
          *,
          room:rooms (
            id,
            name,
            room_code,
            is_private
          ),
          created_by_profile:profiles!room_invitation_links_created_by_fkey (
            full_name,
            email
          )
        `
        )
        .eq("link_token", token)
        .eq("is_active", true)
        .single();

      if (error || !invitationLink) {
        return null;
      }

      // Check if link is expired
      if (new Date(invitationLink.expires_at) < new Date()) {
        return null;
      }

      // Check if max uses reached
      if (
        invitationLink.max_uses &&
        invitationLink.current_uses >= invitationLink.max_uses
      ) {
        return null;
      }

      return invitationLink as InvitationLinkWithDetails;
    } catch (error: any) {
      console.error("Get invitation by token error:", error);
      return null;
    }
  },

  // Join room via invitation link
  async joinRoomViaLink(token: string, userId: string): Promise<void> {
    try {
      const invitationLink = await this.getInvitationByToken(token);

      if (!invitationLink) {
        throw new Error("Invalid or expired invitation link");
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from("room_members")
        .select("id")
        .eq("room_id", invitationLink.room_id)
        .eq("user_id", userId)
        .single();

      if (existingMember) {
        throw new Error("You are already a member of this room");
      }

      // Add user to room
      const { error: memberError } = await supabase
        .from("room_members")
        .insert({
          room_id: invitationLink.room_id,
          user_id: userId,
          role: "member",
        });

      if (memberError) throw memberError;

      // Update link usage count
      const { error: updateError } = await supabase
        .from("room_invitation_links")
        .update({
          current_uses: invitationLink.current_uses + 1,
          updated_at: new Date().toISOString(),
        })
        .eq("id", invitationLink.id);

      if (updateError) {
        console.error("Failed to update link usage:", updateError);
        // Don't throw error here as the user was successfully added
      }

      console.log(
        `User ${userId} joined room ${invitationLink.room_id} via invitation link`
      );
    } catch (error: any) {
      console.error("Join room via link error:", error);
      throw new Error(error.message || "Failed to join room");
    }
  },

  // Accept direct invitation
  async acceptInvitation(invitationId: string, userId: string): Promise<void> {
    try {
      // Get invitation details
      const { data: invitation, error: getError } = await supabase
        .from("room_invitations")
        .select("*")
        .eq("id", invitationId)
        .eq("invited_user", userId)
        .eq("status", "pending")
        .single();

      if (getError || !invitation) {
        throw new Error("Invitation not found or already processed");
      }

      // Check if invitation is expired
      if (new Date(invitation.expires_at) < new Date()) {
        throw new Error("Invitation has expired");
      }

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from("room_members")
        .select("id")
        .eq("room_id", invitation.room_id)
        .eq("user_id", userId)
        .single();

      if (existingMember) {
        throw new Error("You are already a member of this room");
      }

      // Add user to room and update invitation status
      const { error: memberError } = await supabase
        .from("room_members")
        .insert({
          room_id: invitation.room_id,
          user_id: userId,
          role: "member",
        });

      if (memberError) throw memberError;

      // Update invitation status
      const { error: updateError } = await supabase
        .from("room_invitations")
        .update({
          status: "accepted",
          updated_at: new Date().toISOString(),
        })
        .eq("id", invitationId);

      if (updateError) throw updateError;

      console.log(
        `User ${userId} accepted invitation to room ${invitation.room_id}`
      );
    } catch (error: any) {
      console.error("Accept invitation error:", error);
      throw new Error(error.message || "Failed to accept invitation");
    }
  },

  // Decline invitation
  async declineInvitation(invitationId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("room_invitations")
        .update({
          status: "declined",
          updated_at: new Date().toISOString(),
        })
        .eq("id", invitationId)
        .eq("invited_user", userId)
        .eq("status", "pending");

      if (error) throw error;

      console.log(`User ${userId} declined invitation ${invitationId}`);
    } catch (error: any) {
      console.error("Decline invitation error:", error);
      throw new Error(error.message || "Failed to decline invitation");
    }
  },

  // Get user's received invitations
  async getUserInvitations(userId: string): Promise<InvitationWithDetails[]> {
    try {
      // First get the basic invitations
      const { data: invitations, error } = await supabase
        .from("room_invitations")
        .select(
          `
          *,
          rooms (
            id,
            name,
            room_code,
            is_private
          )
        `
        )
        .eq("invited_user", userId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      if (!invitations || invitations.length === 0) {
        return [];
      }

      // Get profile information separately
      const inviterIds = [...new Set(invitations.map((inv) => inv.invited_by))];
      const { data: inviterProfiles } = await supabase
        .from("profiles")
        .select("id, full_name, email")
        .in("id", inviterIds);

      const { data: inviteeProfile } = await supabase
        .from("profiles")
        .select("id, full_name, email")
        .eq("id", userId)
        .single();

      // Combine the data
      const enrichedInvitations = invitations.map((invitation) => ({
        ...invitation,
        room: invitation.rooms,
        invited_by_profile: inviterProfiles?.find(
          (p) => p.id === invitation.invited_by
        ) || {
          full_name: null,
          email: "Unknown",
        },
        invited_user_profile: inviteeProfile || {
          full_name: null,
          email: "Unknown",
        },
      }));

      return enrichedInvitations as InvitationWithDetails[];
    } catch (error: any) {
      console.error("Get user invitations error:", error);
      throw new Error(error.message || "Failed to get invitations");
    }
  },

  // Get room's sent invitations (for room admins)
  async getRoomInvitations(roomId: string): Promise<InvitationWithDetails[]> {
    try {
      // First get the basic invitations
      const { data: invitations, error } = await supabase
        .from("room_invitations")
        .select(
          `
          *,
          rooms (
            id,
            name,
            room_code,
            is_private
          )
        `
        )
        .eq("room_id", roomId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      if (!invitations || invitations.length === 0) {
        return [];
      }

      // Get profile information separately
      const inviterIds = [...new Set(invitations.map((inv) => inv.invited_by))];
      const inviteeIds = [
        ...new Set(invitations.map((inv) => inv.invited_user)),
      ];
      const allUserIds = [...new Set([...inviterIds, ...inviteeIds])];

      const { data: userProfiles } = await supabase
        .from("profiles")
        .select("id, full_name, email")
        .in("id", allUserIds);

      // Combine the data
      const enrichedInvitations = invitations.map((invitation) => ({
        ...invitation,
        room: invitation.rooms,
        invited_by_profile: userProfiles?.find(
          (p) => p.id === invitation.invited_by
        ) || {
          full_name: null,
          email: "Unknown",
        },
        invited_user_profile: userProfiles?.find(
          (p) => p.id === invitation.invited_user
        ) || {
          full_name: null,
          email: "Unknown",
        },
      }));

      return enrichedInvitations as InvitationWithDetails[];
    } catch (error: any) {
      console.error("Get room invitations error:", error);
      throw new Error(error.message || "Failed to get room invitations");
    }
  },

  // Get room's invitation links (for room admins)
  async getRoomInvitationLinks(
    roomId: string
  ): Promise<InvitationLinkWithDetails[]> {
    try {
      // First get the basic invitation links
      const { data: links, error } = await supabase
        .from("room_invitation_links")
        .select(
          `
          *,
          rooms (
            id,
            name,
            room_code,
            is_private
          )
        `
        )
        .eq("room_id", roomId)
        .order("created_at", { ascending: false });

      if (error) throw error;

      if (!links || links.length === 0) {
        return [];
      }

      // Get profile information separately
      const creatorIds = [...new Set(links.map((link) => link.created_by))];
      const { data: creatorProfiles } = await supabase
        .from("profiles")
        .select("id, full_name, email")
        .in("id", creatorIds);

      // Combine the data
      const enrichedLinks = links.map((link) => ({
        ...link,
        room: link.rooms,
        created_by_profile: creatorProfiles?.find(
          (p) => p.id === link.created_by
        ) || {
          full_name: null,
          email: "Unknown",
        },
      }));

      return enrichedLinks as InvitationLinkWithDetails[];
    } catch (error: any) {
      console.error("Get room invitation links error:", error);
      throw new Error(error.message || "Failed to get invitation links");
    }
  },

  // Deactivate invitation link
  async deactivateInvitationLink(
    linkId: string,
    userId: string
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from("room_invitation_links")
        .update({
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq("id", linkId)
        .eq("created_by", userId);

      if (error) throw error;

      console.log(`Invitation link ${linkId} deactivated by user ${userId}`);
    } catch (error: any) {
      console.error("Deactivate invitation link error:", error);
      throw new Error(error.message || "Failed to deactivate invitation link");
    }
  },

  // Cancel invitation (for invitation sender)
  async cancelInvitation(invitationId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("room_invitations")
        .update({
          status: "expired",
          updated_at: new Date().toISOString(),
        })
        .eq("id", invitationId)
        .eq("invited_by", userId)
        .eq("status", "pending");

      if (error) throw error;

      console.log(`Invitation ${invitationId} cancelled by user ${userId}`);
    } catch (error: any) {
      console.error("Cancel invitation error:", error);
      throw new Error(error.message || "Failed to cancel invitation");
    }
  },

  // Generate invitation URL
  generateInvitationUrl(token: string): string {
    const baseUrl = window.location.origin;
    return `${baseUrl}/join-room/${token}`;
  },
};
