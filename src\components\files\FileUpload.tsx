import { useState, useCallback, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { useForm } from "react-hook-form";
import { z } from "zod";
import toast from "react-hot-toast";
import {
  fileService,
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZE,
} from "../../lib/fileService";
import { folderService } from "../../lib/folderService";
import type { Folder } from "../../lib/folderService";
import { useAuth } from "../../contexts/AuthContext";
import { formatFileSize } from "../../lib/utils";

const uploadSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  description: z.string().max(1000, "Description too long").optional(),
  isPublic: z.boolean().optional(),
});

type UploadFormData = z.infer<typeof uploadSchema>;

interface FileUploadProps {
  onUploadComplete?: (document: any) => void;
  onClose?: () => void;
  maxFiles?: number;
  currentFolderId?: string | null;
  showFolderSelector?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: "pending" | "uploading" | "complete" | "error";
  error?: string;
  documentId?: string;
}

export function FileUpload({
  onUploadComplete,
  onClose,
  maxFiles = 10,
  currentFolderId = null,
  showFolderSelector = false,
}: FileUploadProps) {
  const { user } = useAuth();
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(
    currentFolderId
  );
  const [folders, setFolders] = useState<Folder[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UploadFormData>({
    defaultValues: {
      isPublic: false,
    },
  });

  // Load folders when component mounts and folder selector is enabled
  useEffect(() => {
    if (showFolderSelector && user) {
      loadFolders();
    }
  }, [showFolderSelector, user]);

  // Update selected folder when currentFolderId changes
  useEffect(() => {
    setSelectedFolderId(currentFolderId);
  }, [currentFolderId]);

  const loadFolders = async () => {
    if (!user) return;

    try {
      setIsLoadingFolders(true);
      const userFolders = await folderService.getUserFolders(user.id);
      setFolders(userFolders);
    } catch (error: any) {
      console.error("Load folders error:", error);
      toast.error("Failed to load folders");
    } finally {
      setIsLoadingFolders(false);
    }
  };

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length + uploadingFiles.length > maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`);
        return;
      }

      const newFiles: UploadingFile[] = acceptedFiles.map((file) => {
        const validation = fileService.validateFile(file);
        return {
          file,
          progress: 0,
          status: validation.isValid ? "pending" : "error",
          error: validation.isValid ? undefined : validation.errors.join(", "),
        };
      });

      setUploadingFiles((prev) => [...prev, ...newFiles]);
    },
    [uploadingFiles.length, maxFiles]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: Object.keys(ALLOWED_FILE_TYPES).reduce((acc, mimeType) => {
      acc[mimeType] =
        ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize: MAX_FILE_SIZE,
    multiple: true,
  });

  const removeFile = (index: number) => {
    setUploadingFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async (data: UploadFormData) => {
    if (!user) {
      toast.error("You must be logged in to upload files");
      return;
    }

    const filesToUpload = uploadingFiles.filter((f) => f.status === "pending");
    if (filesToUpload.length === 0) {
      toast.error("No valid files to upload");
      return;
    }

    setIsUploading(true);

    try {
      for (let i = 0; i < filesToUpload.length; i++) {
        const fileIndex = uploadingFiles.findIndex(
          (f) => f.file === filesToUpload[i].file
        );

        // Update status to uploading
        setUploadingFiles((prev) =>
          prev.map((f, idx) =>
            idx === fileIndex ? { ...f, status: "uploading" as const } : f
          )
        );

        try {
          const document = await fileService.uploadDocument(
            filesToUpload[i].file,
            data.title || filesToUpload[i].file.name,
            data.description || "",
            user.id,
            data.isPublic || false,
            selectedFolderId
          );

          // Update status to complete
          setUploadingFiles((prev) =>
            prev.map((f, idx) =>
              idx === fileIndex
                ? {
                    ...f,
                    status: "complete" as const,
                    progress: 100,
                    documentId: document.id,
                  }
                : f
            )
          );

          onUploadComplete?.(document);
        } catch (error: any) {
          // Update status to error
          setUploadingFiles((prev) =>
            prev.map((f, idx) =>
              idx === fileIndex
                ? {
                    ...f,
                    status: "error" as const,
                    error: error.message,
                  }
                : f
            )
          );
        }
      }

      const successCount = uploadingFiles.filter(
        (f) => f.status === "complete"
      ).length;
      if (successCount > 0) {
        toast.success(`Successfully uploaded ${successCount} file(s)`);
        reset();
        if (successCount === filesToUpload.length) {
          setUploadingFiles([]);
          onClose?.();
        }
      }
    } catch (error: any) {
      toast.error(error.message || "Upload failed");
    } finally {
      setIsUploading(false);
    }
  };

  const allowedExtensions = Object.values(ALLOWED_FILE_TYPES).flat();

  return (
    <div className="space-y-6">
      {/* Upload Form */}
      <form onSubmit={handleSubmit(uploadFiles)} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme">
            Document Title
          </label>
          <input
            {...register("title")}
            type="text"
            className="input-field"
            placeholder="Enter document title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
              {errors.title.message}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 transition-theme duration-theme">
            Description (Optional)
          </label>
          <textarea
            {...register("description")}
            rows={3}
            className="input-field"
            placeholder="Enter document description"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400 transition-theme duration-theme">
              {errors.description.message}
            </p>
          )}
        </div>

        {/* Folder Selector */}
        {showFolderSelector && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload to Folder (Optional)
            </label>
            <select
              value={selectedFolderId || ""}
              onChange={(e) => setSelectedFolderId(e.target.value || null)}
              className="input-field"
              disabled={isLoadingFolders}
            >
              <option value="">Root (No folder)</option>
              {folders.map((folder) => (
                <option key={folder.id} value={folder.id}>
                  {folder.path.replace(/\//g, " > ").slice(3, -3) ||
                    folder.name}
                </option>
              ))}
            </select>
            {isLoadingFolders && (
              <p className="mt-1 text-sm text-gray-500">Loading folders...</p>
            )}
          </div>
        )}

        <div className="flex items-center">
          <input
            {...register("isPublic")}
            type="checkbox"
            id="isPublic"
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label
            htmlFor="isPublic"
            className="ml-2 block text-sm text-gray-700"
          >
            Make this document public
          </label>
        </div>

        {/* Drag and Drop Area */}
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors duration-200
            ${
              isDragActive
                ? "border-blue-400 bg-blue-50"
                : "border-gray-300 hover:border-blue-400 hover:bg-gray-50"
            }
          `}
        >
          <input {...getInputProps()} />
          <div className="space-y-4">
            <div className="text-4xl">📁</div>
            <div>
              <p className="text-lg font-medium text-gray-900">
                {isDragActive ? "Drop files here" : "Drag & drop files here"}
              </p>
              <p className="text-sm text-gray-600">or click to browse files</p>
            </div>
            <div className="text-xs text-gray-500">
              <p>Supported formats: {allowedExtensions.join(", ")}</p>
              <p>Maximum file size: {formatFileSize(MAX_FILE_SIZE)}</p>
              <p>Maximum {maxFiles} files at once</p>
            </div>
          </div>
        </div>

        {/* File List */}
        {uploadingFiles.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Files to Upload</h4>
            {uploadingFiles.map((uploadFile, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">
                      {fileService.getFileIcon(
                        fileService.getFileType(uploadFile.file.name)
                      )}
                    </span>
                    <div>
                      <p className="font-medium text-gray-900">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(uploadFile.file.size)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {uploadFile.status === "complete" && (
                      <span className="text-green-600">✓</span>
                    )}
                    {uploadFile.status === "error" && (
                      <span className="text-red-600">✗</span>
                    )}
                    {uploadFile.status !== "uploading" && (
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="text-gray-400 hover:text-red-600"
                      >
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>

                {/* Progress Bar */}
                {uploadFile.status === "uploading" && (
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadFile.progress}%` }}
                    />
                  </div>
                )}

                {/* Error Message */}
                {uploadFile.status === "error" && uploadFile.error && (
                  <p className="text-sm text-red-600 mt-2">
                    {uploadFile.error}
                  </p>
                )}

                {/* Success Message */}
                {uploadFile.status === "complete" && (
                  <p className="text-sm text-green-600 mt-2">
                    Upload complete!
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          {onClose && (
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
              disabled={isUploading}
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={
              isUploading ||
              uploadingFiles.filter((f) => f.status === "pending").length === 0
            }
            className="btn-primary"
          >
            {isUploading ? "Uploading..." : "Upload Files"}
          </button>
        </div>
      </form>
    </div>
  );
}
