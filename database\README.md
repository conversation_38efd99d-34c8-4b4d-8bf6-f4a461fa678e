# SmartBagPack Database Setup

This folder contains the essential SQL files for setting up the SmartBagPack database.

## 📁 Files Overview

### `schema.sql`
**Main database schema** - Contains the complete database structure including:
- All tables (profiles, rooms, documents, etc.)
- Row Level Security (RLS) policies
- Foreign key relationships
- Database functions and triggers
- Initial setup for the entire application

**Usage**: Run this first when setting up a new database.

### `invitation-notification-system.sql`
**New features schema** - Contains the invitation and notification system tables:
- `room_invitations` - Direct user invitations to rooms
- `room_invitation_links` - Shareable invitation links
- `notifications` - Comprehensive notification system
- Related RLS policies and functions

**Usage**: Run this if you need to add only the invitation/notification features to an existing database.

### `storage-setup.sql`
**Supabase Storage configuration** - Sets up file storage buckets and policies for:
- Document uploads
- File access permissions
- Storage security policies

**Usage**: Run this to configure Supabase Storage for file uploads.

## 🚀 Setup Instructions

### For New Database:
1. Run `schema.sql` in your Supabase SQL Editor
2. Run `storage-setup.sql` for file upload functionality

### For Existing Database (adding new features):
1. Run `invitation-notification-system.sql` to add invitation and notification features
2. Update your application code to use the new features

## 🔒 Security Notes

All files include proper Row Level Security (RLS) policies to ensure:
- Users can only access their own data
- Proper permissions for room admins
- Secure document sharing
- Protected notification system

## 📝 Notes

- These files are safe to run multiple times (they include `IF NOT EXISTS` checks)
- Always backup your database before running SQL scripts
- Test in a development environment first
